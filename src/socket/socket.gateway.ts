import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';
import {
  ConnectedSocket,
  MessageBody,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  SubscribeMessage,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import { PrismaService } from 'nestjs-prisma';
import { Namespace, Socket } from 'socket.io';
import { AIService } from 'src/AI/AI.service';
import { InteractionService } from 'src/interaction/interaction.service';
import { bad_words } from 'src/libs/bad';

type GamificationContext = {
  appId: string;
  pageType:
    | 'loading'
    | 'create-direct-open-ended'
    | 'create-inpaint-open-ended'
    | 'co-create-direct-open-ended'
    | 'co-create-inpaint-open-ended'
    | 'voting'
    | 'submit-email'
    | 'feedback'
    | 'error';
  sessionId: string;

  assetGenerationId?: string;
  questionId?: string;
};

@WebSocketGateway()
export class SocketGateway
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer() io: Namespace;
  private readonly logger = new Logger(SocketGateway.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly interactionService: InteractionService,
    private readonly aiService: AIService,
  ) {}

  afterInit(): void {
    this.logger.log(`Websocket Gateway initialized.`);
  }

  async handleConnection(client: Socket) {
    this.logger.debug(
      `Client ${client.id} attempting connection with auth: ${JSON.stringify(client.handshake.auth)}`,
    );

    if (
      !this.verifyCredentials(
        client.handshake.auth.apiKey,
        client.handshake.auth.appId,
      )
    ) {
      this.logger.warn(`Client ${client.id} failed authentication`);
      return client.disconnect();
    }

    this.logger.log(`Client ${client.id} connected successfully`);
  }

  async handleDisconnect(client: Socket) {
    this.logger.log(`Session ${client.handshake.query.sessionId}`);
    this.logger.log(`Client ${client.id} disconnected`);
  }

  @SubscribeMessage('join-room')
  async handleJoinRoom(
    @ConnectedSocket() client: Socket,
    @MessageBody() { sessionId }: { sessionId: string },
  ) {
    this.logger.debug(
      `Client ${client.id} attempting to join room with sessionId: ${sessionId}`,
    );

    if (!sessionId) {
      this.logger.warn(`Client ${client.id} did not provide sessionId`);
      return;
    }

    if (!(await this.interactionService.verifySessionStarted(sessionId))) {
      this.logger.warn(`Session ${sessionId} not started`);
      return;
    }

    await client.join(sessionId);
    this.logger.log(`Client ${client.id} joined room ${sessionId}`);

    client.emit('joined-room', {
      sessionId,
      message: `You have joined room ${sessionId}`,
    });
    this.logger.debug(`Emitted 'joined-room' to client ${client.id}`);
  }

  @SubscribeMessage('gamification:room:join')
  async handleJoinRoom2(
    @ConnectedSocket() client: Socket,
    @MessageBody() { sessionId }: { sessionId: string },
  ) {
    this.logger.debug(
      `Client ${client.id} attempting to join gamification room with sessionId: ${sessionId}`,
    );

    if (!sessionId) {
      this.logger.warn(`Client ${client.id} did not provide sessionId`);
      return;
    }

    if (!(await this.interactionService.verifySessionStarted(sessionId))) {
      this.logger.warn(`Session ${sessionId} not started`);
      return;
    }

    await client.join(sessionId);
    this.logger.log(
      `Client ${client.id} joined gamification room ${sessionId}`,
    );
  }

  @SubscribeMessage('gamification:microsite:context:request')
  async handleRequestContext(
    @ConnectedSocket() client: Socket,
    @MessageBody() { sessionId }: { sessionId: string },
  ) {
    this.logger.debug(
      `Client ${client.id} requesting context for sessionId: ${sessionId}`,
    );

    const session =
      await this.interactionService.verifySessionStarted(sessionId);

    if (!session) {
      this.logger.warn(`Invalid session ${sessionId} for context request`);
      client.emit('gamification:feedback:submitted', {
        status: 'error',
        error: 'Invalid session',
        data: null,
      });
      return;
    }

    const gameState = await this.prisma.gameState.findUnique({
      where: { sessionId },
      select: {
        id: true,
        type: true,
        personaSnapshot: true,
        gameQA: { select: { id: true, question: true } },
        generatedImageId: true,
      },
    });

    if (gameState) {
      this.logger.debug(
        `Sending context to client ${client.id}: ${JSON.stringify(gameState)}`,
      );
      client.emit('gamification:microsite:context:received', {
        status: 'success',
        error: null,
        data: {
          id: gameState.id,
          type: gameState.type,
          personaKey: (gameState.personaSnapshot as any)?.key,
          gameQA: gameState.gameQA,
          generatedImageId: gameState.generatedImageId,
        },
      });
    }
    this.io.to(sessionId).emit('gamification:context:request', { sessionId });

    this.logger.debug(`Emitted context request to room ${sessionId}`);
  }

  @SubscribeMessage('gamification:context:send')
  async handleSendContext(
    @ConnectedSocket() client: Socket,
    @MessageBody() context: GamificationContext,
  ) {
    this.logger.debug(
      `Client ${client.id} sending context: ${JSON.stringify(context)}`,
    );

    if (!context.sessionId) {
      this.logger.warn(`Client ${client.id} not in any room`);
      return;
    }

    this.logger.debug(`Broadcasting context to room ${context.sessionId}`);
    this.io.to(context.sessionId).emit('gamification:context:receive', context);
  }

  @SubscribeMessage('gamification:answer:submit')
  async handleSubmitAnswer(
    @ConnectedSocket() client: Socket,
    @MessageBody()
    {
      sessionId,
      questionId,
      answer,
    }: { sessionId: string; questionId: string; answer: string },
  ) {
    this.logger.debug(
      `Client ${client.id} submitting answer for sessionId: ${sessionId}, questionId: ${questionId}`,
    );

    const session =
      await this.interactionService.verifySessionStarted(sessionId);

    if (!session) {
      this.logger.warn(`Invalid session ${sessionId} for answer submission`);
      client.emit('gamification:feedback:submitted', {
        status: 'error',
        error: 'Invalid session',
        data: null,
      });
      return;
    }

    const gameState = await this.prisma.gameState.findUnique({
      where: { sessionId },
    });

    if (!gameState) {
      this.logger.warn(`Game state not found for sessionId: ${sessionId}`);
      client.emit('gamification:feedback:submitted', {
        status: 'error',
        error: 'Invalid session',
        data: null,
      });
      return;
    }

    const currentGameQA = await this.prisma.gameQA.findUnique({
      where: { id: questionId },
    });

    if (!currentGameQA) {
      this.logger.warn(`Question not found: ${questionId}`);
      client.emit('gamification:answer:submitted', {
        status: 'error',
        error: 'Question not found',
        data: null,
      });
      return;
    }

    let finalAnswer: string = answer;

    if (currentGameQA.closed) {
      this.logger.warn(`Question ${questionId} is closed`);
      client.emit('gamification:answer:submitted', {
        status: 'error',
        error: 'Question closed',
        data: null,
      });
    } else {
      const eachAnswersWord = answer.toLowerCase().trim().split(' ');

      const badWords = eachAnswersWord.filter((word) =>
        bad_words.includes(word),
      );

      if (badWords.length > 0) {
        this.logger.warn(`Answer contains bad words: ${badWords.join(', ')}`);
        client.emit('gamification:answer:submitted', {
          status: 'error',
          error: 'Content not appropriate',
          data: null,
        });
        return;
      } else {
        this.logger.debug(`Updating answer for questionId: ${questionId}`);
        if (gameState.type === 'GROUP') {
          finalAnswer = [currentGameQA.answer, answer]
            .filter(Boolean)
            .join('; ');
        } else {
          const siteQuestion = await this.prisma.siteQuestions.findFirst({
            where: {
              siteId: gameState.siteId,
              mode: 'INDIVIDUAL',
              order: currentGameQA.order,
            },
          });

          // No special characters, just pure text
          const cleanAnswer = answer
            .replace(/[^a-zA-Z\s]/g, '')
            .toLowerCase()
            .trim();

          if (cleanAnswer === 'all' || cleanAnswer === 'everything') {
            finalAnswer = (siteQuestion.options as any[])
              .filter(({ isOpenEnded }) => !isOpenEnded)
              .map(({ text }) => text)
              .join(', ');
          }
        }
      }

      await this.prisma.gameQA.update({
        where: { id: questionId },
        data: { answer: finalAnswer },
      });

      this.logger.log(
        `Answer submitted successfully for questionId: ${questionId}`,
      );
      this.io.to(sessionId).emit('gamification:answer:submitted', {
        status: 'success',
        error: null,
        data: answer,
      });
    }
  }

  @SubscribeMessage('gamification:feedback:submit')
  async handleSubmitFeedback(
    @ConnectedSocket() client: Socket,
    @MessageBody()
    {
      sessionId,
      rate,
      feedback,
    }: { sessionId: string; rate: number; feedback?: string },
  ) {
    this.logger.debug(
      `Client ${client.id} submitting feedback for sessionId: ${sessionId}, rate: ${rate}`,
    );

    const session =
      await this.interactionService.verifySessionStarted(sessionId);

    if (!session) {
      this.logger.warn(`Invalid session ${sessionId} for feedback submission`);
      client.emit('gamification:feedback:submitted', {
        status: 'error',
        error: 'Invalid session',
        data: null,
      });
      return;
    }

    const gameState = await this.prisma.gameState.findUnique({
      where: { sessionId },
    });

    if (!gameState) {
      this.logger.warn(`Game state not found for sessionId: ${sessionId}`);
      client.emit('gamification:feedback:submitted', {
        status: 'error',
        error: 'Invalid session',
        data: null,
      });
      return;
    }

    // check for bad words
    if (feedback) {
      const badWords = bad_words.filter(
        (word) => feedback.toLowerCase() === word,
      );
      if (badWords.length > 0) {
        this.logger.warn(`Feedback contains bad words: ${badWords.join(', ')}`);
        client.emit('gamification:feedback:submitted', {
          status: 'error',
          error: 'Content not appropriate',
          data: null,
        });
        return;
      }
    }

    this.logger.debug(`Creating feedback record for sessionId: ${sessionId}`);
    await this.prisma.feedback.create({
      data: {
        appId: gameState.appId,
        sessionId,
        gameStateId: gameState.id,
        rate,
        feedback,
      },
    });

    this.logger.log(
      `Feedback submitted successfully for sessionId: ${sessionId}`,
    );
    this.io.to(sessionId).emit('gamification:feedback:submitted', {
      status: 'success',
      error: null,
      data: {
        rate,
        feedback,
      },
    });
  }

  @SubscribeMessage('gamification:vote:submit')
  async handleSubmitVote(
    @ConnectedSocket() client: Socket,
    @MessageBody()
    {
      sessionId,
      selectedImageId,
    }: { sessionId: string; selectedImageId: string },
  ) {
    this.logger.debug(
      `Client ${client.id} submitting vote for sessionId: ${sessionId}, imageId: ${selectedImageId}`,
    );

    const session =
      await this.interactionService.verifySessionStarted(sessionId);

    if (!session) {
      this.logger.warn(`Invalid session ${sessionId} for vote submission`);
      client.emit('gamification:vote:submitted', {
        status: 'error',
        error: 'Invalid session',
        data: null,
      });
      return;
    }

    const gameState = await this.prisma.gameState.findUnique({
      where: { sessionId },
    });

    if (!gameState) {
      this.logger.warn(`Game state not found for sessionId: ${sessionId}`);
      client.emit('gamification:vote:submitted', {
        status: 'error',
        error: 'Invalid session',
        data: null,
      });
      return;
    }

    this.logger.debug(`Incrementing vote for imageId: ${selectedImageId}`);
    await this.prisma.generatedAsset.update({
      where: { id: selectedImageId, sessionId },
      data: { vote: { increment: 1 } },
    });

    const voteResults = await this.prisma.generatedAsset.findMany({
      where: { sessionId },
      select: { id: true, vote: true },
    });

    this.logger.log(
      `Vote submitted successfully for imageId: ${selectedImageId}`,
    );
    this.io.to(sessionId).emit('gamification:vote:submitted', {
      status: 'success',
      error: null,
      data: voteResults,
    });
  }

  @SubscribeMessage('gamification:email:submit')
  async handleSubmitEmail(
    @ConnectedSocket() client: Socket,
    @MessageBody()
    {
      sessionId,
      email,
      imageId,
    }: { sessionId: string; email: string; imageId: string },
  ) {
    this.logger.debug(
      `Client ${client.id} submitting email for sessionId: ${sessionId}, imageId: ${imageId}`,
    );

    const session =
      await this.interactionService.verifySessionStarted(sessionId);

    if (!session) {
      this.logger.warn(`Invalid session ${sessionId} for email submission`);
      client.emit('gamification:email:submitted', {
        status: 'error',
        error: 'Invalid session',
        data: null,
      });
      return;
    }

    this.logger.debug(
      `Checking existing email for email: ${email}, imageId: ${imageId}`,
    );
    const existingEmail = await this.prisma.email.findFirst({
      where: { email, imageId, type: 'GENERATED' },
    });

    if (existingEmail && existingEmail.videoId) {
      if (existingEmail.emailSent) {
        this.logger.warn(
          `Email already sent for email: ${email}, imageId: ${imageId}`,
        );
        client.emit('gamification:email:submitted', {
          status: 'error',
          error: 'Email already sent',
          data: null,
        });
      } else {
        this.logger.log(
          `Email exists but not sent for email: ${email}, imageId: ${imageId}`,
        );
        client.emit('gamification:email:submitted', {
          status: 'success',
          error: null,
          data: null,
        });
      }
    } else {
      this.logger.debug(`Checking for existing video for imageId: ${imageId}`);
      const existingVideo = await this.prisma.generatedAsset.findUnique({
        where: { fromImageId: imageId },
      });

      if (existingVideo) {
        this.logger.debug(
          `Using existing video ${existingVideo.id} for imageId: ${imageId}`,
        );
        const newEmail = await this.prisma.email.create({
          data: {
            sessionId,
            email,
            imageId,
            videoId: existingVideo.id,
          },
        });

        this.logger.log(
          `Email record created with existing video for sessionId: ${sessionId}`,
        );
        client.emit('gamification:email:submitted', {
          status: 'success',
          error: null,
          data: newEmail.id,
        });
      } else {
        this.logger.debug(
          `Generating new video for sessionId: ${sessionId}, imageId: ${imageId}`,
        );
        const { status, data } = await this.aiService.generateVideo({
          sessionId,
          imageId,
        });

        if (status !== 'success') {
          this.logger.error(
            `Failed to generate video for sessionId: ${sessionId}, imageId: ${imageId}`,
          );
          client.emit('gamification:email:submitted', {
            status: 'error',
            error: 'Failed to generate video',
            data: null,
          });
          return;
        }

        const videoId = data;
        this.logger.debug(`Video generated successfully: ${videoId}`);

        await this.prisma.generatedAsset.update({
          where: { id: videoId },
          data: { fromImageId: imageId },
        });

        const newEmail = await this.prisma.email.create({
          data: {
            sessionId,
            email,
            imageId,
            videoId,
          },
        });

        this.logger.log(
          `Email record created with new video for sessionId: ${sessionId}`,
        );
        client.emit('gamification:email:submitted', {
          status: 'success',
          error: null,
          data: newEmail.id,
        });
      }
    }
  }

  @SubscribeMessage('gamification:photobooth:email:submit')
  async handleSubmitPhotoboothEmail(
    @ConnectedSocket() client: Socket,
    @MessageBody()
    {
      sessionId,
      email,
      imageId,
    }: { sessionId: string; email: string; imageId: string },
  ) {
    this.logger.debug(
      `Client ${client.id} submitting photobooth email for sessionId: ${sessionId}, imageId: ${imageId}`,
    );

    const session =
      await this.interactionService.verifySessionStarted(sessionId);

    if (!session) {
      this.logger.warn(`Invalid session ${sessionId} for email submission`);
      client.emit('gamification:photobooth:email:submitted', {
        status: 'error',
        error: 'Invalid session',
        data: null,
      });
      return;
    }

    this.logger.debug(
      `Checking existing email for email: ${email}, imageId: ${imageId}`,
    );
    const existingEmail = await this.prisma.email.findFirst({
      where: { email, imageId, type: 'PHOTOBOOTH' },
    });

    if (existingEmail) {
      if (existingEmail.emailSent) {
        this.logger.warn(
          `Email already sent for email: ${email}, imageId: ${imageId}`,
        );
        client.emit('gamification:photobooth:email:submitted', {
          status: 'error',
          error: 'Email already sent',
          data: null,
        });
      } else {
        this.logger.log(
          `Email exists but not sent for email: ${email}, imageId: ${imageId}`,
        );
        client.emit('gamification:photobooth:email:submitted', {
          status: 'success',
          error: null,
          data: null,
        });
      }
    } else {
      const newEmail = await this.prisma.email.create({
        data: {
          sessionId,
          email,
          imageId,
          type: 'PHOTOBOOTH',
        },
      });

      this.logger.log(
        `Email record created with new video for sessionId: ${sessionId}`,
      );
      client.emit('gamification:photobooth:email:submitted', {
        status: 'success',
        error: null,
        data: newEmail.id,
      });
    }
  }

  private verifyCredentials(apiKey: string, appId: string) {
    this.logger.debug(`Verifying credentials for appId: ${appId}`);

    const storedApiKey = this.configService.get('API_KEY');
    if (apiKey !== storedApiKey) {
      this.logger.warn(`Invalid API key provided: ${apiKey}`);
      return false;
    }

    const allowedAppIds = this.configService.get<string>('APP_IDS').split(',');
    if (!allowedAppIds || !allowedAppIds.includes(appId)) {
      this.logger.warn(
        `Invalid appId provided: ${appId}. Allowed appIds: ${allowedAppIds.join(', ')}`,
      );
      return false;
    }

    this.logger.debug(`Credentials verified successfully for appId: ${appId}`);
    return true;
  }
}
