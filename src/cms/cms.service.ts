import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { GameStateType } from '@prisma/client';
import { PrismaService } from 'nestjs-prisma';
import {
  GameStateDetailDto,
  GameStateListItemDto,
  GameStateListQueryDto,
  GameStateListResponseDto,
  ImageDetailDto,
  ToggleActiveDto,
  ToggleActiveResponseDto,
} from './cms.dto';
import { GcsService } from 'src/gcs/gcs.service';

@Injectable()
export class CMSService {
  private logger = new Logger(CMSService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly gcsService: GcsService,
  ) {}

  async getGameStates(
    query: GameStateListQueryDto,
  ): Promise<GameStateListResponseDto> {
    const { page = 1, limit = 10, type, appId, siteId, active } = query;
    const parsedPage = parseInt(String(page), 10);
    const parsedLimit = parseInt(String(limit), 10);
    const skip = (parsedPage - 1) * parsedLimit;

    // Build where clause
    const where: any = {
      generatedImageId: { not: null },
    };

    if (type) {
      where.type = type;
    }

    if (appId) {
      where.appId = appId;
    }

    if (siteId) {
      where.siteId = siteId;
    }

    if (active !== undefined) {
      where.active = active === 'true';
    }

    // Get total count for pagination
    const total = await this.prisma.gameState.count({ where });

    // Get game states with related data
    const gameStates = await this.prisma.gameState.findMany({
      where,
      include: {
        _count: {
          select: {
            gameQA: true,
            feedback: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
      skip,
      take: parsedLimit,
    });

    // Transform to DTOs
    const data: GameStateListItemDto[] = await Promise.all(
      gameStates.map(async (gameState) => {
        const generatedAsset = await this.prisma.generatedAsset.findUnique({
          where: { id: gameState.generatedImageId! },
        });

        const imageUrl = generatedAsset
          ? await this.gcsService.getFileUrl(generatedAsset.filename)
          : '';

        return {
          id: gameState.id,
          sessionId: gameState.sessionId,
          type: gameState.type,
          name: gameState.name,
          siteName: (gameState.siteSnapshot as any)?.name,
          createdAt: gameState.createdAt,
          updatedAt: gameState.updatedAt,
          generatedImageId: gameState.generatedImageId!,
          imageUrl,
        };
      }),
    );

    const totalPages = Math.ceil(total / parsedLimit);

    return {
      data,
      pagination: {
        page: parsedPage,
        limit: parsedLimit,
        total,
        totalPages,
      },
    };
  }

  async getGameStateDetail(id: string): Promise<GameStateDetailDto> {
    const gameState = await this.prisma.gameState.findUnique({
      where: { id },
      include: {
        gameQA: {
          orderBy: { order: 'asc' },
        },
        feedback: {
          orderBy: { createdAt: 'desc' },
        },
      },
    });

    if (!gameState) {
      throw new NotFoundException('Game state not found');
    }

    if (!gameState.generatedImageId) {
      throw new NotFoundException('Game state has no generated image');
    }

    if (!gameState.reviewed) {
      await this.prisma.gameState.update({
        where: { id: gameState.id },
        data: { reviewed: true },
      });
    }

    // Get the generated asset
    const generatedAsset = await this.prisma.generatedAsset.findUnique({
      where: { id: gameState.generatedImageId },
      include: {
        assetGeneration: true,
      },
    });

    if (!generatedAsset) {
      throw new NotFoundException('Generated asset not found');
    }

    let imageDetails: ImageDetailDto | ImageDetailDto[];

    if (gameState.type === GameStateType.INDIVIDUAL) {
      // For INDIVIDUAL type, single image with single prompt
      imageDetails = await this.buildImageDetail(
        generatedAsset,
        generatedAsset.assetGeneration?.prompt || 'No prompt available',
      );
    } else {
      // For GROUP type, multiple images with multiple prompts
      imageDetails = await this.buildGroupImageDetails(generatedAsset);
    }

    return {
      id: gameState.id,
      sessionId: gameState.sessionId,
      type: gameState.type,
      name: gameState.name,
      siteSnapshot: gameState.siteSnapshot,
      personaSnapshot: gameState.personaSnapshot,
      createdAt: gameState.createdAt,
      gameQA: gameState.gameQA.map((qa) => ({
        order: qa.order,
        question: qa.question,
        answer: qa.answer,
        closed: qa.closed,
      })),
      feedback: gameState.feedback.map((fb) => ({
        rate: fb.rate,
        feedback: fb.feedback,
        createdAt: fb.createdAt,
      })),
      imageDetails,
    };
  }

  async toggleGameStateActive(
    id: string,
    dto: ToggleActiveDto,
  ): Promise<ToggleActiveResponseDto> {
    const generatedAsset = await this.prisma.generatedAsset.findUnique({
      where: { id },
    });

    if (!generatedAsset) {
      throw new NotFoundException('Generated asset not found');
    }

    const updatedGameState = await this.prisma.gameState.update({
      where: { id: generatedAsset.sessionId },
      data: { active: dto.active },
    });

    return {
      id: updatedGameState.id,
      active: updatedGameState.active,
      updatedAt: updatedGameState.updatedAt,
    };
  }

  private async buildImageDetail(
    generatedAsset: any,
    prompt: string,
    promptLabel?: string,
  ): Promise<ImageDetailDto> {
    return {
      generatedAssetId: generatedAsset.id,
      imageUrl: this.constructImageUrl(generatedAsset.filename),
      prompt,
      promptLabel,
      voteCount: generatedAsset.vote,
      likeCount: generatedAsset.likeCount,
      active: generatedAsset.active,
      model: generatedAsset.model,
      createdAt: generatedAsset.createdAt,
    };
  }

  private async buildGroupImageDetails(
    selectedAsset: any,
  ): Promise<ImageDetailDto[]> {
    if (!selectedAsset.assetGeneration) {
      throw new NotFoundException('Asset generation not found');
    }

    const assetGeneration = selectedAsset.assetGeneration;
    const prompts = assetGeneration.prompts as string[];

    if (!prompts || !Array.isArray(prompts) || prompts.length !== 3) {
      throw new NotFoundException('Invalid prompts for GROUP type');
    }

    // Get all generated assets for this asset generation
    const allAssets = await this.prisma.generatedAsset.findMany({
      where: { assetGenerationId: assetGeneration.id },
      orderBy: { createdAt: 'asc' },
    });

    if (allAssets.length !== 3) {
      this.logger.warn(
        `Expected 3 assets for GROUP type, found ${allAssets.length}`,
      );
    }

    const promptLabels = [
      'What we agree on',
      'Every view counts',
      "Let's dream big",
    ];

    // Map prompts to assets by index
    const imageDetails: ImageDetailDto[] = [];
    for (let i = 0; i < Math.min(prompts.length, allAssets.length); i++) {
      const asset = allAssets[i];
      const prompt = prompts[i];
      const promptLabel = promptLabels[i];

      imageDetails.push(
        await this.buildImageDetail(asset, prompt, promptLabel),
      );
    }

    return imageDetails;
  }

  private constructImageUrl(filename: string): string {
    // Construct the full URL for the image
    // This should match how images are served in your application
    const baseUrl =
      process.env.GCS_BASE_URL || 'https://storage.googleapis.com';
    const bucketName = process.env.GCS_BUCKET_NAME || 'your-bucket';
    return `${baseUrl}/${bucketName}/${filename}`;
  }
}
