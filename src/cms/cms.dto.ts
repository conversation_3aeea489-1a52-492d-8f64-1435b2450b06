import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { GameStateType } from '@prisma/client';
import { Transform, Type } from 'class-transformer';
import {
  IsBoolean,
  IsBooleanString,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  Max,
  Min,
} from 'class-validator';

export class GameStateListQueryDto {
  @ApiPropertyOptional({
    description: 'Page number for pagination',
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    example: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'Filter by game state type',
    enum: GameStateType,
    example: GameStateType.GROUP,
  })
  @IsOptional()
  @IsEnum(GameStateType)
  type?: GameStateType;

  @ApiPropertyOptional({
    description: 'Search by app id',
    example: 'app-1',
  })
  @IsOptional()
  @IsString()
  appId?: string;

  @ApiPropertyOptional({
    description: 'Search by site id',
    example: 'abc',
  })
  @IsOptional()
  @IsString()
  siteId?: string;

  @ApiPropertyOptional({
    description: 'Search by active state',
    example: true,
  })
  @IsOptional()
  @IsBooleanString()
  active?: string;
}

export class GameStateListItemDto {
  @ApiProperty({
    description: 'Game state ID',
    example: 'clx1234567890',
  })
  id: string;

  @ApiProperty({
    description: 'Session ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  sessionId: string;

  @ApiProperty({
    description: 'Game state type',
    enum: GameStateType,
    example: GameStateType.GROUP,
  })
  type: GameStateType;

  @ApiPropertyOptional({
    description: 'Game state name',
    example: 'Community Planning Session',
  })
  name?: string;

  @ApiPropertyOptional({
    description: 'Site name from snapshot',
    example: 'Stagmont Ring Node',
  })
  siteName?: string;

  @ApiProperty({
    description: 'Creation date',
    example: '2024-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Generated image ID',
    example: 'clx1234567890',
  })
  generatedImageId: string;

  @ApiProperty({
    description: 'Image URL',
    example: 'https://storage.googleapis.com/bucket/image.png',
  })
  imageUrl: string;

  @ApiPropertyOptional({
    description: 'Total vote count for the image',
    example: 15,
  })
  voteCount?: number;
}

export class GameStateListResponseDto {
  @ApiProperty({
    description: 'List of game states',
    type: [GameStateListItemDto],
  })
  data: GameStateListItemDto[];

  @ApiProperty({
    description: 'Pagination metadata',
    example: {
      page: 1,
      limit: 10,
      total: 50,
      totalPages: 5,
    },
  })
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export class GameQADto {
  @ApiProperty({
    description: 'Question order',
    example: 0,
  })
  order: number;

  @ApiProperty({
    description: 'Question text',
    example: 'What would you like to see here?',
  })
  question: string;

  @ApiPropertyOptional({
    description: 'Answer text',
    example: 'More green spaces and community areas',
  })
  answer?: string;

  @ApiProperty({
    description: 'Whether the question is closed',
    example: true,
  })
  closed: boolean;
}

export class FeedbackDto {
  @ApiProperty({
    description: 'Rating given',
    example: 4,
  })
  rate: number;

  @ApiPropertyOptional({
    description: 'Feedback text',
    example: 'Great experience overall',
  })
  feedback?: string;

  @ApiProperty({
    description: 'Feedback creation date',
    example: '2024-01-01T00:00:00.000Z',
  })
  createdAt: Date;
}

export class ImageDetailDto {
  @ApiProperty({
    description: 'Generated asset ID',
    example: 'clx1234567890',
  })
  generatedAssetId: string;

  @ApiProperty({
    description: 'Image URL',
    example: 'https://storage.googleapis.com/bucket/image.png',
  })
  imageUrl: string;

  @ApiProperty({
    description: 'Prompt used to generate the image',
    example: 'A modern community space with green areas and recreational facilities',
  })
  prompt: string;

  @ApiPropertyOptional({
    description: 'Prompt label for GROUP type',
    example: 'What we agree on',
  })
  promptLabel?: string;

  @ApiProperty({
    description: 'Vote count',
    example: 15,
  })
  voteCount: number;

  @ApiProperty({
    description: 'Like count',
    example: 8,
  })
  likeCount: number;

  @ApiProperty({
    description: 'Whether the asset is active',
    example: true,
  })
  active: boolean;

  @ApiProperty({
    description: 'Model used for generation',
    example: 'imagen-4.0-generate-preview-06-06',
  })
  model: string;

  @ApiProperty({
    description: 'Asset creation date',
    example: '2024-01-01T00:00:00.000Z',
  })
  createdAt: Date;
}

export class GameStateDetailDto {
  @ApiProperty({
    description: 'Game state ID',
    example: 'clx1234567890',
  })
  id: string;

  @ApiProperty({
    description: 'Session ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  sessionId: string;

  @ApiProperty({
    description: 'Game state type',
    enum: GameStateType,
    example: GameStateType.GROUP,
  })
  type: GameStateType;

  @ApiPropertyOptional({
    description: 'Game state name',
    example: 'Community Planning Session',
  })
  name?: string;

  @ApiPropertyOptional({
    description: 'Site snapshot data',
  })
  siteSnapshot?: any;

  @ApiPropertyOptional({
    description: 'Persona snapshot data',
  })
  personaSnapshot?: any;

  @ApiProperty({
    description: 'Creation date',
    example: '2024-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Game questions and answers',
    type: [GameQADto],
  })
  gameQA: GameQADto[];

  @ApiProperty({
    description: 'Feedback received',
    type: [FeedbackDto],
  })
  feedback: FeedbackDto[];

  @ApiProperty({
    description: 'Image details - single for INDIVIDUAL, array for GROUP',
    oneOf: [
      { $ref: '#/components/schemas/ImageDetailDto' },
      { type: 'array', items: { $ref: '#/components/schemas/ImageDetailDto' } },
    ],
  })
  imageDetails: ImageDetailDto | ImageDetailDto[];
}

export class ToggleActiveDto {
  @ApiPropertyOptional({
    description: 'Set active state. If not provided, current state will be toggled',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  active?: boolean;
}

export class ToggleActiveResponseDto {
  @ApiProperty({
    description: 'Updated generated asset ID',
    example: 'clx1234567890',
  })
  id: string;

  @ApiProperty({
    description: 'New active state',
    example: true,
  })
  active: boolean;

  @ApiProperty({
    description: 'Update timestamp',
    example: '2024-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}
