import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { UserRole } from '@prisma/client';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import { RolesGuard } from 'src/auth/guards/roles.guard';
import {
  GameStateDetailDto,
  GameStateListQueryDto,
  GameStateListResponseDto,
  ToggleActiveDto,
  ToggleActiveResponseDto,
} from './cms.dto';
import { CMSService } from './cms.service';

@ApiTags('CMS')
@Controller('cms')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.ADMIN)
export class CMSController {
  constructor(private readonly cmsService: CMSService) {}

  @Get('app-ids')
  async getAppIds() {
    return process.env.APP_IDS.split(',');
  }

  @Get('game-states')
  async getGameStates(
    @Query() query: GameStateListQueryDto,
  ): Promise<GameStateListResponseDto> {
    return this.cmsService.getGameStates(query);
  }

  @Get('game-states/:id')
  async getGameStateDetail(
    @Param('id') id: string,
  ): Promise<GameStateDetailDto> {
    return this.cmsService.getGameStateDetail(id);
  }

  @Patch('generated-assets/:id/toggle-active')
  async toggleGeneratedAssetActive(
    @Param('id') id: string,
    @Body() dto: ToggleActiveDto,
  ): Promise<ToggleActiveResponseDto> {
    return this.cmsService.toggleGameStateActive(id, dto);
  }
}
