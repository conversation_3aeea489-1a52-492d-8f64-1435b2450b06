import { Injectable } from '@nestjs/common';
import { AssetGenerationType } from '@prisma/client';
import { PrismaService } from 'nestjs-prisma';

@Injectable()
export class SettingService {
  constructor(private prisma: PrismaService) {}

  async getSetting(key: string) {
    const setting = await this.prisma.setting.findUnique({
      where: { key },
    });

    if (!setting) {
      return {
        status: 'error',
        error: 'Setting not found',
        data: null,
      };
    }

    return {
      status: 'success',
      error: null,
      data: setting,
    };
  }

  async updateSetting(key: string, value: string) {
    try {
      const result = await this.handleSpecialSettingKey(
        key,
        <AssetGenerationType>value,
      );

      if (result.status === 'error') {
        return result;
      }

      const updatedSetting = await this.prisma.setting.update({
        where: { key },
        data: { value },
      });

      return {
        status: 'success',
        error: null,
        data: updatedSetting,
      };
    } catch (error) {
      if (error.code === 'P2025') {
        return {
          status: 'error',
          error: 'Setting not found',
          data: null,
        };
      }
    }
  }

  async createSetting(key: string, value: string) {
    try {
      const newSetting = await this.prisma.setting.create({
        data: { key, value },
      });

      return {
        status: 'success',
        error: null,
        data: newSetting,
      };
    } catch (error) {
      if (error.code === 'P2002') {
        return {
          status: 'error',
          error: 'Setting already exists',
          data: null,
        };
      }
    }
  }

  async getSettings() {
    const settings = await this.prisma.setting.findMany();

    return {
      status: 'success',
      error: null,
      data: settings,
    };
  }

  private async handleSpecialSettingKey(
    key: string,
    value: AssetGenerationType,
  ) {
    if (!['video-model', 'image-model', 'inpaint-model'].includes(key)) {
      return;
    }

    const currentSetting = await this.prisma.setting.findUnique({
      where: { key },
    });

    if (!currentSetting) {
      return {
        status: 'error',
        error: 'Setting not found',
        data: null,
      };
    }

    await this.prisma.assetGeneration.updateMany({
      where: {
        status: 'pending',
        type: <AssetGenerationType>currentSetting.value,
      },
      data: { type: value },
    });

    return {
      status: 'success',
      error: null,
      data: null,
    };
  }
}
