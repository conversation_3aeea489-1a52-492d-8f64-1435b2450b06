import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  UseGuards,
} from '@nestjs/common';
import { ApiParam, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { AdminKeyGuard } from 'src/libs/decorators/admin-key.guard';
import { CreateSettingDto } from 'src/setting/setting.dto';
import { SettingService } from 'src/setting/setting.service';

@ApiTags('Settings')
@UseGuards(AdminKeyGuard)
@ApiSecurity('x-api-key')
@Controller('settings')
export class SettingsController {
  constructor(private settingService: SettingService) {}

  @Post()
  async createSetting(@Body() dto: CreateSettingDto) {
    return await this.settingService.createSetting(dto.key, dto.value);
  }

  @Get()
  async getSettings() {
    return await this.settingService.getSettings();
  }

  @Get(':key')
  @ApiParam({
    name: 'key',
    description: 'Setting key',
    required: true,
  })
  async getSetting(@Param('key') key: string) {
    return await this.settingService.getSetting(key);
  }

  @Patch()
  async updateSetting(@Body() dto: CreateSettingDto) {
    return await this.settingService.updateSetting(dto.key, dto.value);
  }
}
