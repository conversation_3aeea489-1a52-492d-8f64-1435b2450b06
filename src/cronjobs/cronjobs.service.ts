import { MailerService } from '@nestjs-modules/mailer';
import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PrismaService } from 'nestjs-prisma';
import { GcsService } from 'src/gcs/gcs.service';

@Injectable()
export class CronJobsService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly gcsService: GcsService,
    private readonly mailerService: MailerService,
  ) {}

  @Cron(CronExpression.EVERY_MINUTE)
  async sendAssetsEmails() {
    const pendingEmails = await this.prisma.$queryRawUnsafe<
      {
        videoId: string;
        emails: string;
        filename: string;
        fromImageId: string;
      }[]
    >(
      `
      SELECT 
        e.videoId, 
        GROUP_CONCAT(e.email) as emails,
        ga.filename,
        ga.fromImageId
      FROM emails e
      JOIN generated_assets ga ON e.videoId = ga.id
      JOIN asset_generations ag ON ga.assetGenerationId = ag.id
      WHERE e.emailSent = false
        AND ag.status = 'completed'
      GROUP BY e.videoId, ga.filename, ga.fromImageId
      LIMIT 10
      `,
    );
    console.log(`Sending emails for ${pendingEmails.length} videos`);

    for (const email of pendingEmails) {
      const generatedImage = await this.prisma.generatedAsset.findUnique({
        where: { id: email.fromImageId },
        select: { sessionId: true, filename: true },
      });

      const { response } = await this.prisma.lLMGeneration.findFirst({
        where: { sessionId: generatedImage.sessionId },
      });

      const { type, siteId } = await this.prisma.gameState.findUnique({
        where: { sessionId: generatedImage.sessionId },
        select: { type: true, siteId: true },
      });

      const site = await this.prisma.sites.findUnique({
        where: { id: siteId },
        select: { name: true },
      });

      let vision_statement_title = '',
        vision_statement = '';

      if (type === 'GROUP') {
        const { what_we_agree_on, every_view_counts, lets_dream_big } =
          JSON.parse(response) as {
            what_we_agree_on: {
              vision_statement_title: string;
              vision_statement: string;
            };
            every_view_counts: {
              vision_statement_title: string;
              vision_statement: string;
            };
            lets_dream_big: {
              vision_statement_title: string;
              vision_statement: string;
            };
          };

        if (generatedImage.filename.includes('-0.png')) {
          vision_statement_title = what_we_agree_on.vision_statement_title;
          vision_statement = what_we_agree_on.vision_statement;
        } else if (generatedImage.filename.includes('-1.png')) {
          vision_statement_title = every_view_counts.vision_statement_title;
          vision_statement = every_view_counts.vision_statement;
        } else if (generatedImage.filename.includes('-2.png')) {
          vision_statement_title = lets_dream_big.vision_statement_title;
          vision_statement = lets_dream_big.vision_statement;
        }
      } else {
        const parsedData = JSON.parse(response) as {
          vision_statement_title: string;
          vision_statement: string;
        };

        vision_statement_title = parsedData.vision_statement_title;
        vision_statement = parsedData.vision_statement;
      }

      const imageBuffer = await this.gcsService.getFileBuffer(
        generatedImage.filename,
      );
      const videoBuffer = await this.gcsService.getFileBuffer(email.filename);

      if (!vision_statement_title || !vision_statement) {
        console.error(
          `Failed to send email for videoId: ${email.videoId}. Missing vision statement title or content`,
        );
        continue;
      }

      for await (const emailAddress of email.emails.split(',')) {
        const result = await this.mailerService.sendMail({
          from: process.env.EMAIL_SENDER,
          to: emailAddress,
          subject: 'Your Dream Lab Creations Are Here!',
          html: `
          <p>Hey there,</p>

          <p>Thank you for visiting <b>Dream Lab!</b> Your unique concept design and video are attached to this email.</p>

          <div style="padding-left: 30px;">
            <p>Your vision for ${site.name}:</p>
            <b style="font-size: 2em;">${vision_statement_title.replace('<h5>', '<h5 style="margin: 0">')}</b>
            ${vision_statement}
          </div>

          <p>We hope you had an amazing time in exploring and co-creating new possibilities for our city. Your ideas inspire us to dream of a better Singapore, together.</p>

          <p>Got more brilliant ideas? Come back and visit us again soon!</p>

          <p>
            For feedback and enquiries, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.
            <br />
            <br />
            Best,
            <br />
            Dream Lab Team
          </p>

          <img width="350" src="https://www.uradreamlab.com/images/dream-logo.png" alt="Dream Lab Logo" />

          <p>---</p>
          <small style="color: #555">
            This exhibition includes images generated or enhanced using Artificial Intelligence (AI) tools. These images are intended to illustrate and assist with the visualisation of planning concepts and ideas for the purpose of public viewing and engagement and do not represent final or approved designs. Any resemblance to real-world places, persons, or designs—past or present—is purely coincidental and unintentional.
            <br />
            <br />
            URA makes no representation or warranty regarding the originality of the images or the ownership or intellectual property rights of any elements within the images. By using or accessing these images, you agree to accept all risks associated with their use and agree to indemnify and hold harmless URA from any claims arising from your use of these images.
            <br />
            <br />
            To the fullest extent permitted by law, URA shall in no event be liable for any damages, loss or expense, direct or indirect, including but without limitation, special or consequential damage, or economic loss arising from or in connection with any use of the images in this exhibition.
          </small>
          `,
          attachments: [
            {
              filename: `your_dreamlab_video.mp4`,
              content: videoBuffer,
            },
            {
              filename: `your_dreamlab_image.png`,
              content: imageBuffer,
            },
          ],
        });

        // Check if email was sent successfully
        if (result && result.accepted && result.accepted.length > 0) {
          // Update emailSent status to true
          await this.prisma.email.updateMany({
            where: {
              videoId: email.videoId,
              email: emailAddress,
              type: 'GENERATED',
            },
            data: { email: '*', emailSent: true },
          });
          console.log(`Email sent successfully for videoId: ${email.videoId}`);
        } else {
          console.error(
            `Failed to send email for videoId: ${email.videoId}`,
            result,
          );
        }
      }
    }

    return {
      status: 'success',
      error: null,
      data: null,
    };
  }

  @Cron(CronExpression.EVERY_30_SECONDS)
  async sendAssetsPhotoboothEmails() {
    const pendingEmails = await this.prisma.$queryRawUnsafe<
      {
        emails: string;
        imageId: string;
      }[]
    >(
      `
      SELECT 
        GROUP_CONCAT(e.email) as emails,
        tga.id as imageId
      FROM emails e
      JOIN temp_generated_assets tga ON e.imageId = tga.id
      WHERE e.emailSent = false
      GROUP BY e.imageId
      LIMIT 10
      `,
    );
    console.log(`Sending emails for ${pendingEmails.length} videos`);

    for (const email of pendingEmails) {
      const { sessionId, filename } =
        await this.prisma.tempGeneratedAsset.findUnique({
          where: { id: email.imageId },
          select: { sessionId: true, filename: true },
        });

      const { response } = await this.prisma.lLMGeneration.findFirst({
        where: { sessionId },
      });

      const { type, siteId, generatedImageId } =
        await this.prisma.gameState.findUnique({
          where: { sessionId, active: true },
          select: { type: true, siteId: true, generatedImageId: true },
        });

      const site = await this.prisma.sites.findUnique({
        where: { id: siteId },
        select: { name: true },
      });

      const generatedImage = await this.prisma.generatedAsset.findUnique({
        where: { id: generatedImageId },
        select: { filename: true },
      });

      let vision_statement_title = '',
        vision_statement = '';

      if (type === 'GROUP') {
        const { what_we_agree_on, every_view_counts, lets_dream_big } =
          JSON.parse(response) as {
            what_we_agree_on: {
              vision_statement_title: string;
              vision_statement: string;
            };
            every_view_counts: {
              vision_statement_title: string;
              vision_statement: string;
            };
            lets_dream_big: {
              vision_statement_title: string;
              vision_statement: string;
            };
          };

        if (generatedImage.filename.includes('-0.png')) {
          vision_statement_title = what_we_agree_on.vision_statement_title;
          vision_statement = what_we_agree_on.vision_statement;
        } else if (generatedImage.filename.includes('-1.png')) {
          vision_statement_title = every_view_counts.vision_statement_title;
          vision_statement = every_view_counts.vision_statement;
        } else if (generatedImage.filename.includes('-2.png')) {
          vision_statement_title = lets_dream_big.vision_statement_title;
          vision_statement = lets_dream_big.vision_statement;
        }
      } else {
        const parsedData = JSON.parse(response) as {
          vision_statement_title: string;
          vision_statement: string;
        };

        vision_statement_title = parsedData.vision_statement_title;
        vision_statement = parsedData.vision_statement;
      }

      const imageBuffer = await this.gcsService.getFileBuffer(filename);

      if (!vision_statement_title || !vision_statement) {
        console.error(
          `Failed to send email for Image Id: ${email.imageId}. Missing vision statement title or content`,
        );
        continue;
      }

      for await (const emailAddress of email.emails.split(',')) {
        const result = await this.mailerService.sendMail({
          from: process.env.EMAIL_SENDER,
          to: emailAddress,
          subject: 'Your Dream Lab Creations Are Here!',
          html: `
          <p>Hey there,</p>

          <p>Thank you for visiting <b>Dream Lab!</b> Your unique concept design are attached to this email.</p>

          <div style="padding-left: 30px;">
            <p>Your vision for ${site.name}:</p>
            <b style="font-size: 2em;">${vision_statement_title.replace('<h5>', '<h5 style="margin: 0">')}</b>
            ${vision_statement}
          </div>

          <p>We hope you had an amazing time in exploring and co-creating new possibilities for our city. Your ideas inspire us to dream of a better Singapore, together.</p>

          <p>Got more brilliant ideas? Come back and visit us again soon!</p>

          <p>
            For feedback and enquiries, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.
            <br />
            <br />
            Best,
            <br />
            Dream Lab Team
          </p>

          <img width="350" src="https://www.uradreamlab.com/images/dream-logo.png" alt="Dream Lab Logo" />

          <p>---</p>
          <small style="color: #555">
            This exhibition includes images generated or enhanced using Artificial Intelligence (AI) tools. These images are intended to illustrate and assist with the visualisation of planning concepts and ideas for the purpose of public viewing and engagement and do not represent final or approved designs. Any resemblance to real-world places, persons, or designs—past or present—is purely coincidental and unintentional.
            <br />
            <br />
            URA makes no representation or warranty regarding the originality of the images or the ownership or intellectual property rights of any elements within the images. By using or accessing these images, you agree to accept all risks associated with their use and agree to indemnify and hold harmless URA from any claims arising from your use of these images.
            <br />
            <br />
            To the fullest extent permitted by law, URA shall in no event be liable for any damages, loss or expense, direct or indirect, including but without limitation, special or consequential damage, or economic loss arising from or in connection with any use of the images in this exhibition.
          </small>
          `,
          attachments: [
            {
              filename: `your_dreamlab_image.png`,
              content: imageBuffer,
            },
          ],
        });

        // Check if email was sent successfully
        if (result && result.accepted && result.accepted.length > 0) {
          // Update emailSent status to true
          await this.prisma.email.updateMany({
            where: {
              imageId: email.imageId,
              email: emailAddress,
              type: 'PHOTOBOOTH',
            },
            data: { email: '*', emailSent: true },
          });
          console.log(`Email sent successfully for imageId: ${email.imageId}`);
        } else {
          console.error(
            `Failed to send email for imageId: ${email.imageId}`,
            result,
          );
        }
      }
    }

    return {
      status: 'success',
      error: null,
      data: null,
    };
  }

  @Cron(CronExpression.EVERY_30_MINUTES)
  async cleanupAssetGeneration() {
    const twoHoursAgo = new Date();
    twoHoursAgo.setHours(twoHoursAgo.getHours() - 2);

    await this.prisma.assetGeneration.updateMany({
      where: {
        status: { in: ['pending', 'processing'] },
        createdAt: { lte: twoHoursAgo },
      },
      data: {
        status: 'timeout',
      },
    });
  }

  @Cron(CronExpression.EVERY_10_MINUTES)
  async cleanupTempGeneratedAsset() {
    await this.prisma.tempGeneratedAsset.deleteMany({
      where: {
        createdAt: { lte: new Date(Date.now() - 1000 * 60 * 60) }, // 1 hour ago
      },
    });
  }
}
