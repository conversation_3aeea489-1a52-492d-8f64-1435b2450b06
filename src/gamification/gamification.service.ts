import { Injectable, Logger } from '@nestjs/common';
import {
  AssetGenerationType,
  GameStateType,
  Personas,
  SiteQuestions,
  Sites,
} from '@prisma/client';
import { PrismaService } from 'nestjs-prisma';
import { join } from 'path';
import { AIService } from 'src/AI/AI.service';
import {
  EndVoteGeneratedImageDto,
  NewGameDto,
  RateAndFeedbackDto,
  SubmitPhotoboothImageDto,
  UpdateGameAnswerDto,
  UpdateGameBaseDto,
  UpdateGameNameDto,
  UpdateGamePersonaDto,
  UpdateGameQuestionDto,
  UpdateGameSiteDto,
  VoteGeneratedImageDto,
} from 'src/gamification/gamification.dto';
import { GcsService } from 'src/gcs/gcs.service';
import { InteractionService } from 'src/interaction/interaction.service';
import { bad_words } from 'src/libs/bad';
import { SocketGateway } from 'src/socket/socket.gateway';
import { v4 as uuidv4 } from 'uuid';
import * as sharp from 'sharp';

type SiteIncludeQuestions = Sites & { questions: SiteQuestions[] };

@Injectable()
export class GamificationService {
  private logger = new Logger(GamificationService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly interactionService: InteractionService,
    private readonly aiService: AIService,
    private gcsService: GcsService,
    private socketGateway: SocketGateway,
  ) {}

  async createGS({ appId, dto }: { appId: string; dto: NewGameDto }) {
    const session = await this.interactionService.verifySessionStarted(
      dto.sessionId,
    );

    if (!session) {
      return { status: 'error', error: 'Invalid session', data: null };
    }

    if (session.appId !== appId) {
      return { status: 'error', error: 'Invalid app', data: null };
    }

    if (
      await this.prisma.gameState.findUnique({
        where: { sessionId: dto.sessionId, appId },
      })
    ) {
      return { status: 'error', error: 'Invalid session', data: null };
    }
    const state = await this.prisma.gameState.create({
      data: {
        appId,
        sessionId: dto.sessionId,
        type: dto.gameType,
      },
    });

    return { status: 'success', error: null, data: state };
  }

  //   This for group only
  async updateGSName({
    appId,
    dto,
  }: {
    appId: string;
    dto: UpdateGameNameDto;
  }) {
    const session = await this.interactionService.verifySessionStarted(
      dto.sessionId,
    );

    if (!session) {
      return { status: 'error', error: 'Invalid session', data: null };
    }

    if (session.appId !== appId) {
      return { status: 'error', error: 'Invalid app', data: null };
    }

    const eachAnswersWord = dto.name.toLowerCase().trim().split(' ');

    const badWords = eachAnswersWord.filter((word) => bad_words.includes(word));

    if (badWords.length > 0) {
      return { status: 'error', error: 'Content not appropriate', data: null };
    }

    const state = await this.prisma.gameState.update({
      where: { id: dto.gameStateId },
      data: { name: dto.name },
    });

    return { status: 'success', error: null, data: state };
  }

  async updateGSSite({
    appId,
    dto,
  }: {
    appId: string;
    dto: UpdateGameSiteDto;
  }) {
    const session = await this.interactionService.verifySessionStarted(
      dto.sessionId,
    );

    if (!session) {
      return { status: 'error', error: 'Invalid session', data: null };
    }

    if (session.appId !== appId) {
      return { status: 'error', error: 'Invalid app', data: null };
    }

    const currentState = await this.prisma.gameState.findUnique({
      where: { id: dto.gameStateId, sessionId: dto.sessionId, appId },
    });

    if (!currentState) {
      return { status: 'error', error: 'Invalid session', data: null };
    }

    const site = await this.prisma.sites.findUnique({
      where: { id: dto.siteId },
      include: { questions: true },
    });

    if (!site) {
      return { status: 'error', error: 'Invalid site selection', data: null };
    }

    site.questions = site.questions.filter(
      ({ mode }) => mode === currentState.type,
    );

    const state = await this.prisma.gameState.update({
      where: { id: dto.gameStateId },
      data: { siteId: dto.siteId, siteSnapshot: site },
    });

    return { status: 'success', error: null, data: state };
  }

  async updateGSPersona({
    appId,
    dto,
  }: {
    appId: string;
    dto: UpdateGamePersonaDto;
  }) {
    const session = await this.interactionService.verifySessionStarted(
      dto.sessionId,
    );

    if (!session) {
      return { status: 'error', error: 'Invalid session', data: null };
    }

    if (session.appId !== appId) {
      return { status: 'error', error: 'Invalid app', data: null };
    }

    const persona = await this.prisma.personas.findUnique({
      where: { id: dto.personaId },
    });

    if (!persona) {
      return {
        status: 'error',
        error: 'Invalid persona selection',
        data: null,
      };
    }

    const count = await this.prisma.gameState.count();

    const state = await this.prisma.gameState.update({
      where: { id: dto.gameStateId },
      data: {
        personaId: dto.personaId,
        personaSnapshot: persona,
        name: `${persona.name.replaceAll(' ', '')}${count}`,
      },
    });

    return { status: 'success', error: null, data: state };
  }

  async createGSQuestion({
    appId,
    dto,
  }: {
    appId: string;
    dto: UpdateGameBaseDto;
  }) {
    const session = await this.interactionService.verifySessionStarted(
      dto.sessionId,
    );

    if (!session) {
      return { status: 'error', error: 'Invalid session', data: null };
    }

    if (session.appId !== appId) {
      return { status: 'error', error: 'Invalid app', data: null };
    }

    const gameState = await this.prisma.gameState.findUnique({
      where: { id: dto.gameStateId, sessionId: dto.sessionId, appId },
    });

    if (!gameState || !gameState.siteSnapshot) {
      return { status: 'error', error: 'Invalid session', data: null };
    }

    const questions = (
      gameState.siteSnapshot as unknown as {
        questions: {
          id: string;
          createdAt: Date;
          updatedAt: Date;
          mode: GameStateType;
          siteId: string | null;
          order: number;
          question: string;
          options: { text: string; isOpenEnded: boolean }[];
        }[];
      }
    ).questions;

    await this.prisma.gameQA.deleteMany({
      where: {
        appId,
        sessionId: dto.sessionId,
        gameStateId: dto.gameStateId,
      },
    });

    await this.prisma.gameQA.createMany({
      data: questions.map(({ order, question }) => {
        return {
          order,
          question,
          appId,
          sessionId: dto.sessionId,
          gameStateId: dto.gameStateId,
        };
      }),
    });

    const updatedGameState = await this.prisma.gameState.findUnique({
      where: { id: dto.gameStateId, sessionId: dto.sessionId, appId },
      include: {
        gameQA: {
          select: { id: true, order: true, closed: true },
          orderBy: { order: 'asc' },
        },
      },
    });

    return { status: 'success', error: null, data: updatedGameState };
  }

  async updateGSQuestion({
    appId,
    dto,
  }: {
    appId: string;
    dto: UpdateGameQuestionDto;
  }) {
    const session = await this.interactionService.verifySessionStarted(
      dto.sessionId,
    );

    if (!session) {
      return { status: 'error', error: 'Invalid session a', data: null };
    }

    if (session.appId !== appId) {
      return { status: 'error', error: 'Invalid app', data: null };
    }

    const gameState = await this.prisma.gameState.findUnique({
      where: { id: dto.gameStateId, sessionId: dto.sessionId, appId },
    });

    if (!gameState || !gameState.siteSnapshot) {
      return { status: 'error', error: 'Invalid session b', data: null };
    }

    await this.prisma.gameQA.update({
      where: { id: dto.questionId },
      data: { closed: dto.closed },
    });

    const updatedGameState = await this.prisma.gameState.findUnique({
      where: { id: dto.gameStateId, sessionId: dto.sessionId, appId },
      include: {
        gameQA: {
          select: { id: true, order: true, closed: true },
          orderBy: { order: 'asc' },
        },
      },
    });

    const allGameQA = await this.prisma.gameQA.findMany({
      where: { gameStateId: dto.gameStateId, sessionId: dto.sessionId, appId },
      select: { closed: true },
    });

    if (allGameQA.every(({ closed }) => closed)) {
      const gameState = await this.prisma.gameState.findUnique({
        where: { id: dto.gameStateId, sessionId: dto.sessionId, appId },
        include: { gameQA: true },
      });

      if (!gameState || !gameState.siteSnapshot) {
        return { status: 'error', error: 'Invalid session c', data: null };
      }

      const selectedSite =
        gameState.siteSnapshot as unknown as SiteIncludeQuestions;
      const selectedPersona = gameState.personaSnapshot as unknown as Personas;

      // Handle LLM text gen
      const llmGenerated = await this.prisma.lLMGeneration.findFirst({
        where: { sessionId: dto.sessionId, appId },
      });

      const assetGeneration = await this.prisma.assetGeneration.findFirst({
        where: { sessionId: dto.sessionId, appId },
      });

      if (llmGenerated) {
        return {
          status: 'error',
          error: 'LLM already generated',
          data: null,
        };
      } else {
        if (gameState.type === 'INDIVIDUAL') {
          Promise.resolve().then(async () => {
            const newLlmGenerated = await this.aiService.generateText({
              sessionId: dto.sessionId,
              trackType: selectedSite.trackType,
              site: selectedSite.name,
              siteTagline: selectedSite.tagLine,
              siteDescription: selectedSite.preamble,
              siteKeyCharacteristics: selectedSite.keyCharacteristics,
              persona: selectedPersona?.name,
              personaDescription: selectedPersona?.description,
              excludedWords: selectedSite.excludedWords,
              q1: gameState.gameQA?.[0]?.question,
              a1: gameState.gameQA?.[0]?.answer,
              q2: gameState.gameQA?.[1]?.question,
              a2: gameState.gameQA?.[1]?.answer,
              q3: gameState.gameQA?.[2]?.question,
              a3: gameState.gameQA?.[2]?.answer,
            });

            if (newLlmGenerated.status === 'error') {
              return { status: 'error', error: 'GenAI error', data: null };
            }

            await this.prisma.wordsCloud.createMany({
              data: newLlmGenerated.data.keywords.map((kw) => ({
                word: kw,
                siteKey: selectedSite.key,
              })),
              skipDuplicates: true,
            });

            const wordscloud = await this.prisma.wordsCloud.findMany({
              where: { siteKey: selectedSite.key },
              select: { word: true },
              take: 60,
            });

            if (!assetGeneration) {
              switch (selectedSite.trackType) {
                case 'DIRECT_GENERATION': {
                  const setting = await this.prisma.setting.findUnique({
                    where: { key: 'image-model' },
                  });

                  await this.aiService.generateImage({
                    sessionId: dto.sessionId,
                    prompt: newLlmGenerated.data.prompt,
                    type:
                      (setting?.value as AssetGenerationType) ??
                      'IMAGE_FLUX_KONTEXT_PRO',
                  });
                  break;
                }

                case 'INPAINTING_GENERATION': {
                  const setting = await this.prisma.setting.findUnique({
                    where: { key: 'inpaint-model' },
                  });

                  await this.aiService.inpaintImage({
                    sessionId: dto.sessionId,
                    prompt: newLlmGenerated.data.prompt,
                    siteKey: selectedSite.key,
                    type:
                      (setting?.value as AssetGenerationType) ??
                      'INPAINTING_FLUX',
                  });
                  break;
                }

                default:
                  this.logger.warn('Unknown tracktype submission');
                  break;
              }
            }

            const aiResults = {
              visionStatementTitle: newLlmGenerated.data.vision_statement_title,
              visionStatement: newLlmGenerated.data.vision_statement,
              reasoning: newLlmGenerated.data.reasoning,
              wordscloud: wordscloud.map(({ word }) => word),
            };

            this.socketGateway.io
              .to(dto.sessionId)
              .emit('gamification:ai:generated', {
                status: 'success',
                error: null,
                data: { aiResults },
              });
          });

          return {
            status: 'success',
            error: null,
            data: {
              aiResults: null,
              gameState: updatedGameState,
            },
          };
        } else {
          Promise.resolve().then(async () => {
            const newLlmGenerated = await this.aiService.generateTextCoCreate({
              sessionId: dto.sessionId,
              trackType: selectedSite.trackType,
              site: selectedSite.name,
              siteTagline: selectedSite.tagLine,
              siteDescription: selectedSite.preamble,
              siteKeyCharacteristics: selectedSite.keyCharacteristics,
              persona: selectedPersona?.name,
              personaDescription: selectedPersona?.description,
              excludedWords: selectedSite.excludedWords,
              q1: gameState.gameQA?.[0]?.question,
              a1: gameState.gameQA?.[0]?.answer,
              q2: gameState.gameQA?.[1]?.question,
              a2: gameState.gameQA?.[1]?.answer,
              q3: gameState.gameQA?.[2]?.question,
              a3: gameState.gameQA?.[2]?.answer,
            });

            if (newLlmGenerated.status === 'error') {
              return { status: 'error', error: 'GenAI error', data: null };
            }

            const allKeywords = [
              ...new Set([
                ...newLlmGenerated.data.what_we_agree_on.keywords,
                ...newLlmGenerated.data.every_view_counts.keywords,
                ...newLlmGenerated.data.lets_dream_big.keywords,
              ]),
            ];

            await this.prisma.wordsCloud.createMany({
              data: allKeywords.map((kw) => ({
                word: kw,
                siteKey: selectedSite.key,
              })),
              skipDuplicates: true,
            });

            const wordscloud = await this.prisma.wordsCloud.findMany({
              where: { siteKey: selectedSite.key },
              select: { word: true },
              take: 60,
            });

            if (!assetGeneration) {
              switch (selectedSite.trackType) {
                case 'DIRECT_GENERATION': {
                  const setting = await this.prisma.setting.findUnique({
                    where: { key: 'image-model' },
                  });

                  await this.aiService.generateImageCoCreate({
                    sessionId: dto.sessionId,
                    prompts: [
                      newLlmGenerated.data.what_we_agree_on.prompt,
                      newLlmGenerated.data.every_view_counts.prompt,
                      newLlmGenerated.data.lets_dream_big.prompt,
                    ],
                    type:
                      (setting?.value as AssetGenerationType) ??
                      'IMAGE_FLUX_KONTEXT_PRO',
                  });
                  break;
                }

                case 'INPAINTING_GENERATION': {
                  const setting = await this.prisma.setting.findUnique({
                    where: { key: 'inpaint-model' },
                  });

                  await this.aiService.inpaintCoCreateImage({
                    sessionId: dto.sessionId,
                    prompts: [
                      newLlmGenerated.data.what_we_agree_on.prompt,
                      newLlmGenerated.data.every_view_counts.prompt,
                      newLlmGenerated.data.lets_dream_big.prompt,
                    ],
                    siteKey: selectedSite.key,
                    type:
                      (setting?.value as AssetGenerationType) ??
                      'INPAINTING_FLUX',
                  });
                  break;
                }

                default:
                  this.logger.warn('Unknown tracktype submission');
                  break;
              }
            }

            const aiResults = {
              visions: [
                {
                  ...newLlmGenerated.data.what_we_agree_on,
                  vision_statement_hard_title: '[Common Ground]',
                },
                {
                  ...newLlmGenerated.data.every_view_counts,
                  vision_statement_hard_title: '[All perspectives]',
                },
                {
                  ...newLlmGenerated.data.lets_dream_big,
                  vision_statement_hard_title: '[Breakthrough ideas]',
                },
              ],
              wordscloud: wordscloud.map(({ word }) => word),
            };

            this.socketGateway.io
              .to(dto.sessionId)
              .emit('gamification:ai:generated', {
                status: 'success',
                error: null,
                data: { aiResults },
              });
          });

          return {
            status: 'success',
            error: null,
            data: {
              aiResults: null,
              gameState: updatedGameState,
            },
          };
        }
      }
    }

    return {
      status: 'success',
      error: null,
      data: {
        aiResults: null,
        gameState: updatedGameState,
      },
    };
  }

  async updateGSAnswer({
    appId,
    dto,
  }: {
    appId: string;
    dto: UpdateGameAnswerDto;
  }) {
    const session = await this.interactionService.verifySessionStarted(
      dto.sessionId,
    );

    if (!session) {
      return { status: 'error', error: 'Invalid session', data: null };
    }

    if (session.appId !== appId) {
      return { status: 'error', error: 'Invalid app', data: null };
    }

    const gameState = await this.prisma.gameState.findUnique({
      where: { id: dto.gameStateId, sessionId: dto.sessionId, appId },
      select: { type: true, siteId: true },
    });

    if (!gameState) {
      return { status: 'error', error: 'Invalid session', data: null };
    }

    const currentGameQA = await this.prisma.gameQA.findUnique({
      where: { id: dto.questionId },
    });

    if (currentGameQA.closed) {
      return { status: 'error', error: 'Question closed', data: null };
    }

    let answer: string = dto.answer;

    if (dto.isOpenEnded) {
      if (gameState.type === 'GROUP') {
        answer = [currentGameQA.answer, dto.answer].filter(Boolean).join('; ');
      } else {
        const siteQuestion = await this.prisma.siteQuestions.findFirst({
          where: {
            siteId: gameState.siteId,
            mode: 'INDIVIDUAL',
            order: currentGameQA.order,
          },
        });

        // No special characters, just pure text
        const cleanAnswer = answer
          .replace(/[^a-zA-Z\s]/g, '')
          .toLowerCase()
          .trim();

        if (cleanAnswer === 'all' || cleanAnswer === 'everything') {
          answer = (siteQuestion.options as any[])
            .filter(({ isOpenEnded }) => !isOpenEnded)
            .map(({ text }) => text)
            .join(', ');
        }
      }
    }

    await this.prisma.gameQA.update({
      where: { id: dto.questionId },
      data: { answer },
    });

    return {
      status: 'success',
      error: null,
      data: await this.prisma.gameQA.findUnique({
        where: { id: dto.questionId },
      }),
    };
  }

  async getWordsCloud({ siteKey, count }: { siteKey: string; count: number }) {
    const wordscloud = await this.prisma.wordsCloud.findMany({
      where: { siteKey },
      select: { word: true },
      take: +count,
    });

    return {
      status: 'success',
      error: null,
      data: wordscloud.map(({ word }) => word),
    };
  }

  async getGeneratedAssets(assetGenerationId: string) {
    const assetGeneration = await this.prisma.assetGeneration.findUnique({
      where: { id: assetGenerationId },
    });

    if (!assetGeneration) {
      return {
        status: 'error',
        error: 'Asset generation not found',
        data: null,
      };
    }

    const assets = await this.prisma.generatedAsset.findMany({
      where: { assetGenerationId },
      orderBy: { createdAt: 'asc' },
    });

    const assetsWithUrl = await Promise.all(
      assets.map(async (asset) => {
        return {
          id: asset.id,
          url: await this.gcsService.getFileUrl(asset.filename),
        };
      }),
    );

    return {
      status: 'success',
      error: null,
      data: assetsWithUrl,
    };
  }

  async submitPhotoboothImage({
    appId,
    dto,
    file,
  }: {
    appId: string;
    dto: SubmitPhotoboothImageDto;
    file: Express.Multer.File;
  }) {
    try {
      const session = await this.interactionService.verifySessionStarted(
        dto.sessionId,
      );

      if (!session) {
        return { status: 'error', error: 'Invalid session', data: null };
      }

      if (session.appId !== appId) {
        return { status: 'error', error: 'Invalid app', data: null };
      }

      const gameState = await this.prisma.gameState.findUnique({
        where: { id: dto.gameStateId, sessionId: dto.sessionId, appId },
      });

      if (!gameState || !gameState.siteSnapshot) {
        return { status: 'error', error: 'Invalid session', data: null };
      }

      const assetGeneration = await this.prisma.assetGeneration.findUnique({
        where: { id: dto.assetGenerationId },
      });

      if (!assetGeneration) {
        return {
          status: 'error',
          error: 'Invalid asset generation',
          data: null,
        };
      }

      let watermarkedBuffer: Buffer;

      switch (assetGeneration.type) {
        case 'IMAGE_FLUX_11_PRO_ULTRA': {
          const watermarkPath = join(
            process.cwd(),
            'watermarks',
            'flux-ultra-watermark.png',
          );

          watermarkedBuffer = await sharp(file.buffer)
            .composite([
              {
                input: watermarkPath,
                gravity: 'center',
                blend: 'over',
              },
            ])
            .toBuffer();
          break;
        }

        case 'IMAGE_GEN_4': {
          const watermarkPath = join(
            process.cwd(),
            'watermarks',
            'imagen4-watermark.png',
          );

          watermarkedBuffer = await sharp(file.buffer)
            .composite([
              {
                input: watermarkPath,
                gravity: 'center',
                blend: 'over',
              },
            ])
            .toBuffer();
          break;
        }

        case 'IMAGE_FLUX_KONTEXT_PRO': {
          const watermarkPath = join(
            process.cwd(),
            'watermarks',
            'kontext-watermark.png',
          );

          watermarkedBuffer = await sharp(file.buffer)
            .composite([
              {
                input: watermarkPath,
                gravity: 'center',
                blend: 'over',
              },
            ])
            .toBuffer();
          break;
        }

        case 'IMAGE_FLUX_11_PRO': {
          const watermarkPath = join(
            process.cwd(),
            'watermarks',
            'flux-watermark.png',
          );

          watermarkedBuffer = await sharp(file.buffer)
            .composite([
              {
                input: watermarkPath,
                gravity: 'center',
                blend: 'over',
              },
            ])
            .toBuffer();
          break;
        }

        case 'INPAINTING_FLUX': {
          const watermarkPath = join(
            process.cwd(),
            'watermarks',
            'flux-inpaint-watermark.png',
          );

          watermarkedBuffer = await sharp(file.buffer)
            .composite([
              {
                input: watermarkPath,
                gravity: 'center',
                blend: 'over',
              },
            ])
            .toBuffer();
          break;
        }

        default:
          break;
      }

      if (!watermarkedBuffer) {
        return {
          status: 'error',
          error: 'Failed to watermark image',
          data: null,
        };
      }

      const today = new Date().toISOString().split('T')[0].replace(/-/g, '');
      const tempDestination = join(
        appId,
        today,
        'photobooth',
        dto.sessionId,
        `${uuidv4()}.png`,
      );

      await this.gcsService.uploadBuffer(
        watermarkedBuffer,
        tempDestination,
        'image/png',
      );

      const generatedAsset = await this.prisma.tempGeneratedAsset.create({
        data: {
          appId,
          sessionId: dto.sessionId,
          filename: tempDestination,
        },
      });

      return {
        status: 'success',
        error: null,
        data: generatedAsset,
      };
    } catch (error) {
      this.logger.error('Error submitting photobooth image', error);

      return {
        status: 'error',
        error: 'Failed to submit photobooth image',
        data: null,
      };
    }
  }

  async voteGeneratedImage({
    appId,
    dto,
  }: {
    appId: string;
    dto: VoteGeneratedImageDto;
  }) {
    const session = await this.interactionService.verifySessionStarted(
      dto.sessionId,
    );

    if (!session) {
      return { status: 'error', error: 'Invalid session', data: null };
    }

    if (session.appId !== appId) {
      return { status: 'error', error: 'Invalid app', data: null };
    }

    const gameState = await this.prisma.gameState.findUnique({
      where: { id: dto.gameStateId, sessionId: dto.sessionId, appId },
    });

    if (!gameState || !gameState.siteSnapshot) {
      return { status: 'error', error: 'Invalid session', data: null };
    }

    await this.prisma.generatedAsset.update({
      where: { id: dto.generatedAssetId, sessionId: dto.sessionId, appId },
      data: { vote: { increment: 1 } },
    });

    return {
      status: 'success',
      error: null,
      data: await this.prisma.generatedAsset.findMany({
        where: { sessionId: dto.sessionId, appId },
        select: { id: true, vote: true },
      }),
    };
  }

  async endVote({
    appId,
    dto,
  }: {
    appId: string;
    dto: EndVoteGeneratedImageDto;
  }) {
    const session = await this.interactionService.verifySessionStarted(
      dto.sessionId,
    );

    if (!session) {
      return { status: 'error', error: 'Invalid session', data: null };
    }

    if (session.appId !== appId) {
      return { status: 'error', error: 'Invalid app', data: null };
    }

    const gameState = await this.prisma.gameState.findUnique({
      where: { id: dto.gameStateId, sessionId: dto.sessionId, appId },
    });

    if (!gameState || !gameState.siteSnapshot) {
      return { status: 'error', error: 'Invalid session', data: null };
    }

    let selectedImageId = '';

    if (dto.generatedAssetId) {
      selectedImageId = dto.generatedAssetId;
    } else {
      const votedImages = await this.prisma.generatedAsset.findMany({
        where: { sessionId: dto.sessionId },
        select: { id: true, vote: true, filename: true },
      });

      const highestVoteImage = votedImages.reduce((prev, current) => {
        return prev.vote > current.vote ? prev : current;
      });

      selectedImageId = highestVoteImage.id;
    }

    await this.prisma.gameState.update({
      where: { id: gameState.id },
      data: { generatedImageId: selectedImageId },
    });

    let commentsResult = [];

    if (gameState.type === 'INDIVIDUAL') {
      const selectedPersona = gameState.personaSnapshot as unknown as Personas;

      const { data, error } = await this.aiService.generateComments({
        sessionId: dto.sessionId,
        personaKey: selectedPersona.key,
        generatedImageId: selectedImageId,
      });

      if (error) {
        return { status: 'error', error, data: null };
      }

      commentsResult = data.comments;
    } else if (gameState.type === 'GROUP') {
      const { data, error } = await this.aiService.generateComments({
        sessionId: dto.sessionId,
        personaKey: '',
        generatedImageId: selectedImageId,
      });

      if (error) {
        return { status: 'error', error, data: null };
      }

      commentsResult = data.comments;
    }

    return {
      status: 'success',
      error: null,
      data: { comments: commentsResult },
    };
  }

  async getGeneratedAsset(assetGenerationId: string) {
    const assetGeneration = await this.prisma.assetGeneration.findUnique({
      where: { id: assetGenerationId },
    });

    if (!assetGeneration) {
      return {
        status: 'error',
        error: 'Asset generation not found',
        data: null,
      };
    }

    const gameState = await this.prisma.gameState.findUnique({
      where: { sessionId: assetGeneration.sessionId },
    });

    if (!gameState) {
      return { status: 'error', error: 'Invalid session', data: null };
    }

    const assets = await this.prisma.generatedAsset.findMany({
      where: { assetGenerationId },
      orderBy: { createdAt: 'asc' },
      select: { rawFilename: true, filename: true, id: true },
    });

    const visionTypes = ['vision0', 'vision1', 'vision2'];

    const regex = /-\d+\.png/;

    const images = await Promise.all(
      assets.map(async (asset) => {
        const match = asset.filename.match(regex);
        const fullMatch = match[0];
        const extractedNumber = fullMatch.replace('-', '').replace('.png', '');

        return {
          order: parseInt(extractedNumber),
          id: asset.id,
          rawUrl: await this.gcsService.getFileUrl(asset.rawFilename),
          url: await this.gcsService.getFileUrl(asset.filename),
          visionType:
            gameState.type === 'GROUP'
              ? visionTypes[parseInt(extractedNumber)]
              : null,
        };
      }),
    );

    return {
      status: 'success',
      error: null,
      data: { images: images.sort((a, b) => a.order - b.order) },
    };
  }

  async rateAndFeedback({
    appId,
    dto,
  }: {
    appId: string;
    dto: RateAndFeedbackDto;
  }) {
    const session = await this.interactionService.verifySessionStarted(
      dto.sessionId,
    );

    if (!session) {
      return { status: 'error', error: 'Invalid session', data: null };
    }

    if (session.appId !== appId) {
      return { status: 'error', error: 'Invalid app', data: null };
    }

    const gameState = await this.prisma.gameState.findUnique({
      where: { id: dto.gameStateId, sessionId: dto.sessionId, appId },
    });

    if (!gameState) {
      return { status: 'error', error: 'Invalid session', data: null };
    }

    await this.prisma.feedback.create({
      data: {
        appId,
        sessionId: dto.sessionId,
        gameStateId: dto.gameStateId,
        rate: dto.rate,
        feedback: dto.feedback,
      },
    });

    return { status: 'success', error: null, data: null };
  }

  async getEmailInfo({ emailId }: { emailId: string }) {
    const email = await this.prisma.email.findUnique({
      where: { id: emailId },
    });

    if (!email) {
      return { status: 'error', error: 'Email not found', data: null };
    }

    return { status: 'success', error: null, data: email };
  }
}
