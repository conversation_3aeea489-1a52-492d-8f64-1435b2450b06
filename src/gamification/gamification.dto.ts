import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { GameStateType } from '@prisma/client';
import {
  IsBoolean,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';

export class NewGameDto {
  @ApiProperty({
    description: 'Session ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  @IsUUID('4')
  sessionId: string;

  @ApiProperty({
    description: 'Event type',
    example: GameStateType.INDIVIDUAL + '|' + GameStateType.GROUP,
    enum: GameStateType,
  })
  @IsString()
  @IsEnum(GameStateType)
  gameType: GameStateType;
}

export class UpdateGameBaseDto {
  @ApiProperty({
    description: 'Session ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  @IsUUID('4')
  sessionId: string;

  @ApiProperty({
    description: 'GameState ID',
    example: 'cmboju4yk0001i6d5vbrlcwba',
  })
  @IsString()
  gameStateId: string;
}

export class UpdateGameNameDto extends UpdateGameBaseDto {
  @ApiProperty({
    description: 'Name',
    example: 'Best Groups',
  })
  @IsString()
  name: string;
}

export class UpdateGameSiteDto extends UpdateGameBaseDto {
  @ApiProperty({
    description: 'SiteId',
    example: 'cmboju4yk0001i6d5vbrlcwba',
  })
  @IsString()
  siteId: string;
}

export class UpdateGamePersonaDto extends UpdateGameBaseDto {
  @ApiProperty({
    description: 'personaId',
    example: 'cmboju4yk0001i6d5vbrlcwba',
  })
  @IsString()
  personaId: string;
}

export class UpdateGameQuestionDto extends UpdateGameBaseDto {
  @ApiProperty({
    description: 'questionId',
    example: 'cmboju4yk0001i6d5vbrlcwba',
  })
  @IsString()
  questionId: string;

  @ApiProperty({
    description: 'Question open/close',
    example: true,
  })
  @IsBoolean()
  closed: boolean;
}

export class UpdateGameAnswerDto extends UpdateGameBaseDto {
  @ApiProperty({
    description: 'questionId',
    example: 'cmboju4yk0001i6d5vbrlcwba',
  })
  @IsString()
  questionId: string;

  @ApiProperty({
    description: 'Answer',
    example: 'snapshot purpose',
  })
  @IsString()
  answer: string;

  @ApiProperty({
    description: 'Question is open ended',
    example: true,
  })
  @IsBoolean()
  isOpenEnded: boolean;
}

export class VoteGeneratedImageDto extends UpdateGameBaseDto {
  @ApiProperty({
    description: 'questionId',
    example: 'cmboju4yk0001i6d5vbrlcwba',
  })
  @IsString()
  generatedAssetId: string;
}

export class EndVoteGeneratedImageDto extends UpdateGameBaseDto {
  @ApiPropertyOptional({
    description: 'questionId',
    example: 'cmboju4yk0001i6d5vbrlcwba',
  })
  @IsString()
  @IsOptional()
  generatedAssetId?: string;
}

export class SubmitPhotoboothImageDto extends UpdateGameBaseDto {
  @ApiProperty({
    description: 'Asset generation id',
    example: 'cmboju4yk0001i6d5vbrlcwba',
  })
  @IsString()
  assetGenerationId: string;
}

export class RateAndFeedbackDto extends UpdateGameBaseDto {
  @ApiProperty({
    description: 'Rating',
    example: 5,
  })
  @IsNumber()
  rate: number;

  @ApiPropertyOptional({
    description: 'Feedback',
    example: 'Good job',
  })
  @IsString()
  @IsOptional()
  feedback?: string;
}
