import { Modu<PERSON> } from '@nestjs/common';
import { AIModule } from 'src/AI/AI.module';
import { GamificationController } from 'src/gamification/gamification.controller';
import { GamificationService } from 'src/gamification/gamification.service';
import { GcsModule } from 'src/gcs/gcs.module';
import { InteractionModule } from 'src/interaction/interaction.module';
import { SocketModule } from 'src/socket/socket.module';

@Module({
  imports: [InteractionModule, AIModule, GcsModule, SocketModule],
  controllers: [GamificationController],
  providers: [GamificationService],
  exports: [GamificationService],
})
export class GamificationModule {}
