import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBody, ApiConsumes, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { memoryStorage } from 'multer';
import {
  EndVoteGeneratedImageDto,
  NewGameDto,
  RateAndFeedbackDto,
  SubmitPhotoboothImageDto,
  UpdateGameAnswerDto,
  UpdateGameBaseDto,
  UpdateGameNameDto,
  UpdateGamePersonaDto,
  UpdateGameQuestionDto,
  UpdateGameSiteDto,
  VoteGeneratedImageDto,
} from 'src/gamification/gamification.dto';
import { GamificationService } from 'src/gamification/gamification.service';
import { ApiKeyGuard } from 'src/libs/decorators/api-key.guard';
import { AppId } from 'src/libs/decorators/app-id';
import { AppIdGuard } from 'src/libs/decorators/app-id.guard';

@ApiTags('Gamifications')
@UseGuards(ApiKeyGuard, AppIdGuard)
@ApiSecurity('x-api-key')
@ApiSecurity('x-app-id')
@Controller('gamification')
export class GamificationController {
  constructor(private gamificationService: GamificationService) {}

  @Post('game-state')
  async newGame(@AppId() appId: string, @Body() dto: NewGameDto) {
    return this.gamificationService.createGS({ appId, dto });
  }

  @Patch('game-state/name')
  async updateGSName(@AppId() appId: string, @Body() dto: UpdateGameNameDto) {
    return this.gamificationService.updateGSName({ appId, dto });
  }

  @Patch('game-state/site')
  async updateGSSite(@AppId() appId: string, @Body() dto: UpdateGameSiteDto) {
    return this.gamificationService.updateGSSite({ appId, dto });
  }

  @Patch('game-state/persona')
  async updateGSPersona(
    @AppId() appId: string,
    @Body() dto: UpdateGamePersonaDto,
  ) {
    return this.gamificationService.updateGSPersona({ appId, dto });
  }

  @Post('game-state/qa')
  async createGameQA(@AppId() appId: string, @Body() dto: UpdateGameBaseDto) {
    return this.gamificationService.createGSQuestion({ appId, dto });
  }

  @Patch('game-state/qa/question')
  async updateGSQuestion(
    @AppId() appId: string,
    @Body() dto: UpdateGameQuestionDto,
  ) {
    return this.gamificationService.updateGSQuestion({ appId, dto });
  }

  @Patch('game-state/qa/answer')
  async updateGSAnswer(
    @AppId() appId: string,
    @Body() dto: UpdateGameAnswerDto,
  ) {
    return this.gamificationService.updateGSAnswer({ appId, dto });
  }

  @Get('game-state/words-cloud/:siteKey')
  async getWordsCloud(
    @Param('siteKey') siteKey: string,
    @Query('count') count: number = 60,
  ) {
    return this.gamificationService.getWordsCloud({ siteKey, count });
  }

  @Post('game-state/images/vote')
  async voteGeneratedImage(
    @AppId() appId: string,
    @Body() dto: VoteGeneratedImageDto,
  ) {
    return this.gamificationService.voteGeneratedImage({ appId, dto });
  }

  @Post('game-state/images/vote/end')
  async endVote(@AppId() appId: string, @Body() dto: EndVoteGeneratedImageDto) {
    return this.gamificationService.endVote({ appId, dto });
  }

  @Get('game-state/images/:assetGenerationId')
  async getGeneratedImage(
    @Param('assetGenerationId') assetGenerationId: string,
  ) {
    return this.gamificationService.getGeneratedAsset(assetGenerationId);
  }

  @Post('game-state/photobooth/submit')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        sessionId: { type: 'string' },
        gameStateId: { type: 'string' },
        assetGenerationId: { type: 'string' },
        file: { type: 'string', format: 'binary' },
      },
    },
  })
  @UseInterceptors(
    FileInterceptor('file', {
      storage: memoryStorage(),
      limits: { fileSize: 1024 * 1024 * 10 }, // 10MB,
      fileFilter: (req, file, cb) => {
        if (!file.originalname.match(/\.(jpg|jpeg|png)$/)) {
          return cb(new Error('Only image files are allowed!'), false);
        }
        cb(null, true);
      },
    }),
  )
  async submitPhotoboothImage(
    @AppId() appId: string,
    @Body() dto: SubmitPhotoboothImageDto,
    @UploadedFile() file: Express.Multer.File,
  ) {
    return this.gamificationService.submitPhotoboothImage({ appId, dto, file });
  }

  @Post('game-state/rate-and-feedback')
  async rateAndFeedback(
    @AppId() appId: string,
    @Body() dto: RateAndFeedbackDto,
  ) {
    return this.gamificationService.rateAndFeedback({ appId, dto });
  }

  @Get('game-state/email/:emailId')
  async getEmailInfo(@Param('emailId') emailId: string) {
    return this.gamificationService.getEmailInfo({ emailId });
  }
}
