// src/gcs/gcs.service.ts
import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { Storage, Bucket } from '@google-cloud/storage';
import { Readable } from 'stream';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class GcsService {
  private storage: Storage;
  private bucket: Bucket;

  constructor(private readonly configService: ConfigService) {
    this.storage = new Storage();
    this.bucket = this.storage.bucket(
      this.configService.get<string>('GCS_BUCKET_NAME'),
    );
  }

  async uploadBuffer(
    buffer: Buffer,
    destination: string,
    contentType: string,
  ): Promise<string> {
    const file = this.bucket.file(destination);
    const passthroughStream = new Readable();
    passthroughStream.push(buffer);
    passthroughStream.push(null);

    return new Promise((resolve, reject) => {
      const writeStream = file.createWriteStream({
        metadata: { contentType },
        resumable: false,
      });

      passthroughStream
        .pipe(writeStream)
        .on('finish', () => resolve(`gs://${this.bucket.name}/${destination}`))
        .on('error', (err) => {
          reject(
            new InternalServerErrorException(
              `GCS upload error: ${err.message}`,
            ),
          );
        });
    });
  }

  async getFileUrl(filename: string): Promise<string> {
    const file = this.bucket.file(filename);
    const [url] = await file.getSignedUrl({
      version: 'v4',
      action: 'read',
      expires: Date.now() + 1000 * 60 * 60 * 24 * 1, // 1 days
    });
    return url;
  }

  getFileURI(filename: string) {
    return `gs://${this.configService.get<string>('GCS_BUCKET_NAME')}/${filename}`;
  }

  async getFileBuffer(filename: string): Promise<Buffer> {
    const file = this.bucket.file(filename);
    const [buffer] = await file.download();
    return buffer;
  }
}
