import { Body, Controller, Ip, Post, UseGuards } from '@nestjs/common';
import { ApiSecurity, ApiTags } from '@nestjs/swagger';
import { TrackEventDto } from 'src/interaction/interaction.dto';
import { InteractionService } from 'src/interaction/interaction.service';
import { ApiKeyGuard } from 'src/libs/decorators/api-key.guard';
import { AppId } from 'src/libs/decorators/app-id';
import { AppIdGuard } from 'src/libs/decorators/app-id.guard';
import { UserAgent } from 'src/libs/decorators/user-agent';

@ApiTags('Interactions')
@UseGuards(ApiKeyGuard, AppIdGuard)
@ApiSecurity('x-api-key')
@ApiSecurity('x-app-id')
@Controller('interactions')
export class InteractionController {
  constructor(private interactionService: InteractionService) {}

  @Post('start')
  async startSession(
    @Ip() ipAddress: string,
    @UserAgent() userAgent: string,
    @AppId() appId: string,
  ): Promise<{ sessionId: string }> {
    return this.interactionService.startSession({
      ipAddress,
      userAgent,
      appId,
    });
  }

  @Post('event')
  async trackEvent(
    @Body() dto: TrackEventDto,
    @Ip() ipAddress: string,
    @UserAgent() userAgent: string,
    @AppId() appId: string,
  ) {
    return this.interactionService.trackEvent({
      dto,
      ipAddress,
      userAgent,
      appId,
    });
  }

  // @Get('sessions')
  // @ApiQuery({ name: 'sessionId', required: false })
  // @ApiQuery({ name: 'type', required: false })
  // async getSession(
  //   @Query('sessionId') sessionId: string,
  //   @Query('type') type: string,
  // ) {
  //   return this.interactionService.getSession(sessionId, type);
  // }
}
