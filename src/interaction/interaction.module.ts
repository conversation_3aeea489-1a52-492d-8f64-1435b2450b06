import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { InteractionController } from 'src/interaction/interaction.controller';
import { InteractionService } from 'src/interaction/interaction.service';

@Module({
  controllers: [InteractionController],
  imports: [ConfigModule.forRoot()],
  providers: [InteractionService],
  exports: [InteractionService],
})
export class InteractionModule {}
