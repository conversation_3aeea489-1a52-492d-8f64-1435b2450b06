import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsObject, IsOptional, IsString, IsUUID } from 'class-validator';

export class TrackEventDto {
  @ApiProperty({
    description: 'Session ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  @IsUUID('4')
  sessionId: string;

  @ApiProperty({
    description: 'Event type',
    example: 'page_view',
  })
  @IsString()
  eventType: string;

  @ApiProperty({
    description: 'Page URL',
    example: '/home',
  })
  @IsString()
  pageUrl: string;
}
