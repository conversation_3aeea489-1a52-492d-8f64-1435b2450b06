import { MailerModule } from '@nestjs-modules/mailer';
import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { PrismaModule } from 'nestjs-prisma';
import { AIModule } from 'src/AI/AI.module';
import { AuthModule } from 'src/auth/auth.module';
import { CMSModule } from 'src/cms/cms.module';
import { CronJobsModule } from 'src/cronjobs/cronjobs.module';
import { GamificationModule } from 'src/gamification/gamification.module';
import { InteractionModule } from 'src/interaction/interaction.module';
import { PageContentModule } from 'src/page-content/page-content.module';
import { QueueModule } from 'src/queue/queue.module';
import { SettingModule } from 'src/setting/setting.module';
import { SocketModule } from 'src/socket/socket.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    PrismaModule.forRoot({
      isGlobal: true,
    }),
    BullModule.forRoot({
      redis: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT),
        password: process.env.REDIS_PASSWORD,
      },
    }),
    MailerModule.forRoot({
      transport: {
        host: process.env.EMAIL_HOST,
        port: parseInt(process.env.EMAIL_PORT),
        secure: false,
        tls: { rejectUnauthorized: true },
        auth: {
          user: process.env.EMAIL_USER,
          pass: process.env.EMAIL_PASSWORD,
        },
      },
    }),
    ScheduleModule.forRoot(),
    AuthModule,
    AIModule,
    InteractionModule,
    PageContentModule,
    GamificationModule,
    SocketModule,
    QueueModule,
    CronJobsModule,
    SettingModule,
    CMSModule
  ],
})
export class MainModule { }
