import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { MainModule } from 'src/main.module';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(MainModule);

  app.useGlobalPipes(new ValidationPipe());

  app.useStaticAssets('storage');

  const config = new DocumentBuilder()
    .setTitle('URA AI API')
    .setDescription('The URA AI API Swagger Documentation')
    .setVersion('1.0')
    .addServer('http://localhost:51732')
    .addServer('https://dreamlab.interactive-experience.tech/api')
    .addServer('https://www.uradreamlab.com/api')
    .addApiKey({ type: 'api<PERSON><PERSON>', name: 'x-api-key', in: 'header' }, 'x-api-key')
    .addApiKey({ type: 'api<PERSON>ey', name: 'x-app-id', in: 'header' }, 'x-app-id')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('docs', app, document);

  await app.listen(process.env.PORT || 3000);
}
bootstrap();
