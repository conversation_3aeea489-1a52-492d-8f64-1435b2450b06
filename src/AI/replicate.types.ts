export type SDInpaintInput = {
  prompt?: string;
  image: string; // URI
  mask: string; // URI
  height?:
    | 64
    | 128
    | 192
    | 256
    | 320
    | 384
    | 448
    | 512
    | 576
    | 640
    | 704
    | 768
    | 832
    | 896
    | 960
    | 1024;
  width?:
    | 64
    | 128
    | 192
    | 256
    | 320
    | 384
    | 448
    | 512
    | 576
    | 640
    | 704
    | 768
    | 832
    | 896
    | 960
    | 1024;
  negative_prompt?: string;
  num_inference_steps?: number; // 1 to 500
  guidance_scale?: number; // 1 to 20
  scheduler?:
    | 'DDIM'
    | 'K_EULER'
    | 'DPMSolverMultistep'
    | 'K_EULER_ANCESTRAL'
    | 'PNDM'
    | 'KLMS';
  seed?: number;
  disable_safety_checker?: boolean;
};

export type SDInpaintOutput = {
  completed_at: string;
  created_at: string;
  data_removed: boolean;
  error: any; // Can be null or an object depending on the case
  id: string;
  input: {
    image: string;
    mask: string;
    output_format: string;
    prompt: string;
    imageGenerationId: string;
  };
  logs: string;
  metrics: { predict_time: number };
  model: string;
  output: string[];
  started_at: string;
  status: 'succeeded' | 'failed' | 'processing' | string; // Use union type for known values
  urls: {
    cancel: string;
    get: string;
    stream: string;
    web: string;
  };
  version: string;
  webhook: string;
  webhook_events_filter: string[];
};

export type FluxGenInput = {
  prompt: string;
  imageGenerationId: string;
};

export type FluxInpaintInput = {
  prompt: string;
  image: string; // URI to jpeg/png/gif/webp image
  mask: string; // URI to jpeg/png/gif/webp mask image
  seed?: number;
  steps?: number; // 15 to 50, default: 50
  guidance?: number; // 1.5 to 100, default: 60
  prompt_upsampling?: boolean; // default: false
  safety_tolerance?: number; // 1 to 6, default: 2
};

export type FluxInpaintOutput = {
  completed_at: string;
  created_at: string;
  data_removed: boolean;
  error: any; // Can be null or an object depending on the case
  id: string;
  input: {
    image: string;
    mask: string;
    output_format: string;
    prompt: string;
    imageGenerationId: string;
    saveIndex: number;
  };
  logs: string;
  metrics: {
    image_count: number;
    predict_time: number;
  };
  model: string;
  output: string;
  started_at: string;
  status: 'succeeded' | 'failed' | 'processing' | string; // Use union type for known values
  urls: {
    cancel: string;
    get: string;
    stream: string;
    web: string;
  };
  version: string;
  webhook: string;
  webhook_events_filter: string[];
};
