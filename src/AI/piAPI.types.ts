import { GeneratedImage } from '@google/genai';

export type PiAPIResponse = {
  timestamp: number;
  data: {
    task_id: string;
    model: string;
    task_type: string;
    status: string;
    config: {
      service_mode: string;
      webhook_config: Record<string, any>; // Replace with a specific type if known
    };
    input: {
      aspect_ratio: string;
      process_mode: string;
      prompt: string;
      skip_prompt_check: boolean;
    };
    output: {
      // image
      image_url: string;
      image_urls: string[];
      temporary_image_urls: string[];
      discord_image_url: string;
      actions: string[];
      progress: number;
      intermediate_image_urls: string[] | null;

      // Video
      video_url: string;
      type: 'm2v_img2video';
      status: number;
      works: {
        content_type: 'video';
        status: number;
        type: 'm2v_img2video';
        cover: {
          resource: string;
          resource_without_watermark: string;
          height: number;
          width: number;
          duration: number;
        };
        video: {
          resource: string;
          resource_without_watermark: string;
          height: number;
          width: number;
          duration: number;
        };
      }[];

      video_raw: {
        url: string;
      };

      // gen4
      images: GeneratedImage[];
    };
    meta: {
      created_at: string;
      started_at: string;
      ended_at: string;
      usage: Record<string, any>; // Replace with a specific type if known
      is_using_private_pool: boolean;
      model_version: string;
      process_mode: string;
      failover_triggered: boolean;
    };
    detail: string | null;
    logs: string[];
    error: {
      code: number;
      raw_message: string;
      message: string;
      detail: string | null;
    };
  };
};
