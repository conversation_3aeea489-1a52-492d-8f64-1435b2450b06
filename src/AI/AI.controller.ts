import { Body, Controller, Get, Param, Post, UseGuards } from '@nestjs/common';
import { ApiSecurity, ApiTags } from '@nestjs/swagger';
import {
  GenerateCommentsDto,
  GenerateImageDto,
  GenerateInpaintingImageDto,
  GenerateTextDto,
  GenerateTopicsDto,
  SandboxGenerateVideoDto,
} from 'src/AI/AI.dto';
import { AIService } from 'src/AI/AI.service';
import { PiAPIResponse } from 'src/AI/piAPI.types';
import { FluxInpaintOutput, SDInpaintOutput } from 'src/AI/replicate.types';
import { ApiKeyGuard } from 'src/libs/decorators/api-key.guard';
import { WebhookKeyGuard } from 'src/libs/decorators/webhook-key.guard';

@ApiTags('AI')
@Controller('ai')
export class AIController {
  constructor(private aiService: AIService) { }

  @UseGuards(ApiKeyGuard)
  @ApiSecurity('x-api-key')
  @Post('text/generate')
  async generateText(@Body() dto: GenerateTextDto) {
    return this.aiService.generateText(dto);
  }

  @UseGuards(ApiKeyGuard)
  @ApiSecurity('x-api-key')
  @Post('text/topics')
  async generateTopics(@Body() dto: GenerateTopicsDto) {
    return this.aiService.generateTopics(dto);
  }

  @UseGuards(ApiKeyGuard)
  @ApiSecurity('x-api-key')
  @Post('text/insights')
  async generateInsights(@Body() dto: GenerateTopicsDto) {
    return this.aiService.generateInsights(dto);
  }

  @UseGuards(ApiKeyGuard)
  @ApiSecurity('x-api-key')
  @Post('text/comments')
  async genComments(@Body() dto: GenerateCommentsDto) {
    return this.aiService.generateComments(dto);
  }

  @UseGuards(ApiKeyGuard)
  @ApiSecurity('x-api-key')
  @Post('image/generate')
  async generateImage(@Body() dto: GenerateImageDto) {
    return this.aiService.generateImage(dto);
  }

  @UseGuards(ApiKeyGuard)
  @ApiSecurity('x-api-key')
  @Post('image/inpaint/flux')
  async inpaintFluxImage(@Body() dto: GenerateInpaintingImageDto) {
    return this.aiService.sandboxInpaintImage({ model: 'flux', dto });
  }

  @UseGuards(ApiKeyGuard)
  @ApiSecurity('x-api-key')
  @Post('image/inpaint/sd')
  async inpaintSDImage(@Body() dto: GenerateInpaintingImageDto) {
    return this.aiService.sandboxInpaintImage({ model: 'sd', dto });
  }

  @UseGuards(ApiKeyGuard)
  @ApiSecurity('x-api-key')
  @Post('video/generate')
  async generateVideo(@Body() dto: SandboxGenerateVideoDto) {
    return this.aiService.sandboxGenerateVideo(dto);
  }

  @UseGuards(ApiKeyGuard)
  @ApiSecurity('x-api-key')
  @Get('asset/generate/:assetGenerationId')
  async getGeneratedImage(
    @Param('assetGenerationId') assetGenerationId: string,
  ) {
    return this.aiService.getGeneratedAsset(assetGenerationId);
  }

  @UseGuards(WebhookKeyGuard)
  @ApiSecurity('x-webhook-secret')
  @Post('webhook/piapi')
  async piapiWebhook(@Body() dto: PiAPIResponse) {
    return this.aiService.piapiWebhook(dto);
  }

  @Post('webhook/replicate/flux')
  async replicateFluxWebhook(@Body() dto: FluxInpaintOutput) {
    return this.aiService.replicateFluxWebhook(dto);
  }

  @Post('webhook/replicate/sd')
  async replicateSDWebhook(@Body() dto: SDInpaintOutput) {
    return this.aiService.replicateSDWebhook(dto);
  }
}
