import { HttpService } from '@nestjs/axios';
import { BadRequestException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AxiosError } from 'axios';
import { catchError, firstValueFrom } from 'rxjs';
import { PiAPIResponse } from 'src/AI/piAPI.types';

@Injectable()
export class PiAPIService {
  private readonly piApiUrl = this.configService.get('PIAPI_URL');
  private readonly piApiKey = this.configService.get('PIAPI_KEY');

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  async createMidjourneyTask(prompt: string): Promise<PiAPIResponse> {
    const { data } = await firstValueFrom(
      this.httpService
        .post(
          `${this.piApiUrl}/task`,
          {
            model: 'midjourney',
            task_type: 'imagine',
            input: {
              prompt: `${prompt} --v 7 --ar 16:9`,
              aspect_ratio: '16:9',
              process_mode: 'turbo',
              skip_prompt_check: false,
            },
            config: {
              webhook_config: {
                endpoint: this.configService.get('PIAPI_WEBHOOK_ENDPOINT'),
                secret: this.configService.get('PIAPI_WEBHOOK_SECRET'),
              },
            },
          },
          {
            headers: {
              'Content-Type': 'application/json',
              'x-api-key': this.piApiKey,
            },
          },
        )
        .pipe(
          catchError((error: AxiosError) => {
            throw new BadRequestException(error.response?.data);
          }),
        ),
    );

    return data;
  }

  async getImagineTask(taskId: string) {
    const { data } = await firstValueFrom(
      this.httpService
        .get(`${this.piApiUrl}/task/${taskId}`, {
          headers: { 'x-api-key': this.piApiKey },
        })
        .pipe(
          catchError((error: AxiosError) => {
            throw new BadRequestException(error.response?.data);
          }),
        ),
    );

    return data;
  }

  async createKlingTask(imageUrl: string) {
    const { data } = await firstValueFrom(
      this.httpService
        .post(
          `${this.piApiUrl}/task`,
          {
            model: 'kling',
            task_type: 'video_generation',
            input: {
              prompt:
                "Create a 5-second video with a very subtle slow zoom-in effect on the original image. Maintain the scene's composition, lighting, and framing. No camera panning or tilting. The only movement should be a gentle, steady zoom-in to simulate slight depth and immersion, as if the camera slowly moves forward. No added effects, no animation, no parallax. Pure photographic realism with slight zoom",
              image_url: imageUrl,
              cfg_scale: 0.5,
              duration: 5,
              aspect_ratio: '16:9',
              camera_control: {
                type: 'simple',
                config: {
                  horizontal: 0,
                  vertical: 0,
                  pan: 0,
                  tilt: 0,
                  roll: 0,
                  zoom: 0,
                },
              },
              mode: 'std',
            },
            config: {
              webhook_config: {
                endpoint: this.configService.get('PIAPI_WEBHOOK_ENDPOINT'),
                secret: this.configService.get('PIAPI_WEBHOOK_SECRET'),
              },
            },
          },
          {
            headers: {
              'Content-Type': 'application/json',
              'x-api-key': this.piApiKey,
            },
          },
        )
        .pipe(
          catchError((error: AxiosError) => {
            throw new BadRequestException(error.response?.data);
          }),
        ),
    );

    return data;
  }

  async createLumaTask(imageUrl: string) {
    const { data } = await firstValueFrom(
      this.httpService
        .post(
          `${this.piApiUrl}/task`,
          {
            model: 'luma',
            task_type: 'video_generation',
            input: {
              prompt:
                "Create a 5-second video with a very subtle slow zoom-in effect on the original image. Maintain the scene's composition, lighting, and framing. No camera panning or tilting. The only movement should be a gentle, steady zoom-in to simulate slight depth and immersion, as if the camera slowly moves forward. No added effects, no animation, no parallax. Pure photographic realism with slight zoom",
              key_frames: {
                frame0: {
                  type: 'image',
                  url: imageUrl,
                },
              },
              model_name: 'ray-v1',
              duration: 5,
              aspect_ratio: '16:9',
            },
            config: {
              webhook_config: {
                endpoint: this.configService.get('PIAPI_WEBHOOK_ENDPOINT'),
                secret: this.configService.get('PIAPI_WEBHOOK_SECRET'),
              },
            },
          },
          {
            headers: {
              'Content-Type': 'application/json',
              'x-api-key': this.piApiKey,
            },
          },
        )
        .pipe(
          catchError((error: AxiosError) => {
            throw new BadRequestException(error.response?.data);
          }),
        ),
    );

    return data;
  }
}
