import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { AssetGenerationType, TrackType } from '@prisma/client';
import {
  IsBoolean,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';

export class GenerateTextDto {
  @ApiProperty({
    description: 'Session ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  @IsUUID('4')
  sessionId: string;

  @ApiProperty({
    description: 'Persona',
    example: 'John Doe',
  })
  @IsString()
  persona: string;

  @ApiProperty({
    description: 'Persona Description',
    example: 'A brief description of the persona',
  })
  @IsString()
  personaDescription: string;

  @ApiProperty({
    description: 'Site',
    example: 'Tampines Central',
  })
  @IsString()
  site: string;

  @ApiProperty({
    description: 'Site tagline',
    example: 'A vibrant community hub in the heart of Tampines.',
  })
  @IsString()
  siteTagline: string;

  @ApiProperty({
    description: 'Site Description',
    example: 'A vibrant community hub in the heart of Tampines.',
  })
  @IsString()
  siteDescription: string;

  @ApiProperty({
    description: 'Site Key Characteristics',
    example: 'Family-friendly, Accessible, Green Spaces',
  })
  @IsString()
  siteKeyCharacteristics: string;

  @ApiPropertyOptional({
    description: 'Question 1',
    example: 'What should be the main experience at the site?',
  })
  @IsString()
  @IsOptional()
  q1?: string;

  @ApiPropertyOptional({
    description: 'Question 2',
    example: 'What values are important to you?',
  })
  @IsString()
  @IsOptional()
  q2?: string;

  @ApiPropertyOptional({
    description: 'Question 3',
    example: 'How should this site engage the public?',
  })
  @IsString()
  @IsOptional()
  q3?: string;

  @ApiPropertyOptional({
    description: 'Answer 1',
    example:
      'The main purpose of the site is to provide a space for the public to relax and enjoy the nature.',
  })
  @IsString()
  @IsOptional()
  a1?: string;

  @ApiPropertyOptional({
    description: 'Answer 2',
    example:
      'The main purpose of the site is to provide a space for the public to relax and enjoy the nature.',
  })
  @IsString()
  @IsOptional()
  a2?: string;

  @ApiPropertyOptional({
    description: 'Answer 3',
    example:
      'The main purpose of the site is to provide a space for the public to relax and enjoy the nature.',
  })
  @IsString()
  @IsOptional()
  a3?: string;

  @ApiPropertyOptional({
    description: 'systemPrompt',
    example: 'Words that we dont want to see in the generated prompt',
  })
  @IsString()
  @IsOptional()
  systemInstruction?: string;

  @ApiPropertyOptional({
    description: 'excludedWords',
    example: 'Words that we dont want to see in the generated prompt',
  })
  @IsString()
  @IsOptional()
  excludedWords?: string;

  @ApiPropertyOptional({
    description: 'TrackType',
    example: 'Words that we dont want to see in the generated prompt',
  })
  @IsString()
  @IsEnum(TrackType)
  @IsOptional()
  trackType?: TrackType;
}

export class GenerateTopicsDto {
  @ApiProperty({
    description: 'Session ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  @IsUUID('4')
  sessionId: string;
}

export class GenerateCommentsDto {
  @ApiProperty({
    description: 'Session ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  @IsUUID('4')
  sessionId: string;

  @ApiProperty({
    description: 'Persona',
    example: `Some one`,
  })
  @IsString()
  personaKey: string;

  @ApiProperty({
    description: 'generatedImageId',
    example: `Some one`,
  })
  @IsString()
  generatedImageId: string;
}

export class GenerateImageDto {
  @ApiProperty({
    description: 'Session ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  @IsUUID('4')
  sessionId: string;

  @ApiProperty({
    description: 'Image prompt',
    example: 'A beautiful sunset over a calm ocean',
  })
  @IsString()
  prompt: string;

  @ApiProperty({
    description: 'Image prompt',
    example: AssetGenerationType.IMAGE_GEN_4,
    enum: AssetGenerationType,
  })
  @IsEnum(AssetGenerationType)
  type: AssetGenerationType;

  @ApiPropertyOptional({
    description: 'Image prompts',
    example: [
      'A beautiful sunset over a calm ocean',
      'A beautiful sunset over a calm ocean',
    ],
  })
  @IsString({ each: true })
  @IsOptional()
  prompts?: string[];
}

export class GenerateCoCreateImageDto {
  @ApiProperty({
    description: 'Session ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  @IsUUID('4')
  sessionId: string;

  @ApiProperty({
    description: 'Image prompt',
    example: AssetGenerationType.IMAGE_GEN_4,
    enum: AssetGenerationType,
  })
  @IsEnum(AssetGenerationType)
  type: AssetGenerationType;

  @ApiProperty({
    description: 'Image prompts',
    example: [
      'A beautiful sunset over a calm ocean',
      'A beautiful sunset over a calm ocean',
    ],
  })
  @IsString({ each: true })
  prompts: string[];
}

export class SandboxGenerateVideoDto {
  @ApiProperty({
    description: 'Session ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  @IsUUID('4')
  sessionId: string;

  @ApiProperty({
    description: 'Image Url',
    example: 'Image URL as reference for video gen',
  })
  @IsString()
  imageUrl: string;
}

export class GenerateVideoDto {
  @ApiProperty({
    description: 'Session ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  @IsUUID('4')
  sessionId: string;

  @ApiProperty({
    description: 'Image id',
    example: 'Image id as reference for video gen',
  })
  @IsString()
  imageId: string;
}

export class GenerateInpaintingImageDto {
  @ApiProperty({
    description: 'Session ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  @IsUUID('4')
  sessionId: string;

  @ApiProperty({
    description: 'Site key',
    example: 'key-name',
  })
  @IsString()
  siteKey: string;

  @ApiProperty({
    description: 'Prompt',
    example: 'A beautiful sunset over a calm ocean',
  })
  @IsString()
  prompt: string;

  @ApiProperty({
    description: 'Image prompt',
    example: AssetGenerationType.INPAINTING_FLUX,
    enum: AssetGenerationType,
  })
  @IsEnum(AssetGenerationType)
  type: AssetGenerationType;
}

export class GenerateCoCreateInpaintingImageDto {
  @ApiProperty({
    description: 'Session ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  @IsUUID('4')
  sessionId: string;

  @ApiProperty({
    description: 'Site key',
    example: 'key-name',
  })
  @IsString()
  siteKey: string;

  @ApiProperty({
    description: 'Prompt',
    example: ['A beautiful sunset over a calm ocean'],
  })
  @IsString({ each: true })
  prompts: string[];

  @ApiProperty({
    description: 'Image prompt',
    example: AssetGenerationType.INPAINTING_FLUX,
    enum: AssetGenerationType,
  })
  @IsEnum(AssetGenerationType)
  type: AssetGenerationType;
}
