import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import type Replicate from 'replicate';
import {
  FluxGenInput,
  FluxInpaintInput,
  SDInpaintInput,
} from 'src/AI/replicate.types';

@Injectable()
export class ReplicateService {
  private replicate: Replicate;

  constructor(private readonly configService: ConfigService) {
    const Replicate = require('replicate');
    this.replicate = new Replicate();
  }

  async fluxGen(input: FluxGenInput, saveIndex: number, isUltra = false) {
    this.replicate.run(
      `black-forest-labs/flux-1.1-pro${isUltra ? '-ultra' : ''}`,
      {
        input: {
          ...input,
          raw: isUltra,
          saveIndex: saveIndex,
          aspect_ratio: '16:9',
          output_format: 'png',
          output_quality: 100,
          safety_tolerance: 2,
        },
        webhook: this.configService.get('REPLICATE_FLUX_WEBHOOK_ENDPOINT'),
        webhook_events_filter: ['completed'],
      },
    );
  }

  async fluxContext(input: FluxGenInput, saveIndex: number) {
    this.replicate.run(`black-forest-labs/flux-kontext-pro`, {
      input: {
        ...input,
        saveIndex,
        aspect_ratio: '16:9',
        output_format: 'png',
        output_quality: 100,
        safety_tolerance: 2,
      },
      webhook: this.configService.get('REPLICATE_FLUX_WEBHOOK_ENDPOINT'),
      webhook_events_filter: ['completed'],
    });
  }

  async fluxInpaint(input: FluxInpaintInput, saveIndex: number) {
    this.replicate.run('black-forest-labs/flux-fill-pro', {
      input: {
        ...input,
        saveIndex,
        output_format: 'png',
        safety_tolerance: 2,
      },
      webhook: this.configService.get('REPLICATE_FLUX_WEBHOOK_ENDPOINT'),
      webhook_events_filter: ['completed'],
    });
  }

  async sdInpaint(input: SDInpaintInput, numberOutputs = 4) {
    this.replicate.run(
      'stability-ai/stable-diffusion-inpainting:95b7223104132402a9ae91cc677285bc5eb997834bd2349fa486f53910fd68b3',
      {
        input: {
          ...input,
          width: 1024,
          height: 576,
          num_outputs: numberOutputs,
        },
        webhook: this.configService.get('REPLICATE_SD_WEBHOOK_ENDPOINT'),
        webhook_events_filter: ['completed'],
      },
    );
  }
}
