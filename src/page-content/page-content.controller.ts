import { <PERSON>, Get, Param, Post, Query, UseGuards } from '@nestjs/common';
import { ApiParam, ApiQuery, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { TrackType } from '@prisma/client';
import { bad_words } from 'src/libs/bad';
import { ApiKeyGuard } from 'src/libs/decorators/api-key.guard';
import { PageContentService } from 'src/page-content/page-content.services';

@ApiTags('Page Content')
@UseGuards(ApiKeyGuard)
@ApiSecurity('x-api-key')
@Controller('page-content')
export class PageContentController {
  constructor(private readonly pageContentService: PageContentService) {}

  @Get()
  @ApiQuery({ name: 'trackType', enum: TrackType })
  async getPageContent(@Query('trackType') trackType: TrackType) {
    return this.pageContentService.getPageContent(trackType);
  }

  @Get('assets')
  @ApiQuery({
    name: 'siteKey',
    description:
      'Filter by site key - optional if not provided, return all images',
    required: false,
  })
  @ApiQuery({
    name: 'count',
    description: 'Number of images to return',
    required: false,
  })
  @ApiQuery({
    name: 'sortBy',
    description: 'Sort by - possible values: like',
    required: false,
  })
  @ApiQuery({
    name: 'sortOrder',
    description: 'Sort order - possible values: asc, desc',
    required: false,
  })
  async getGeneratedImage(
    @Query('siteKey') siteKey?: string,
    @Query('count') count: number = 10,
    @Query('sortBy') sortBy?: string,
    @Query('sortOrder') sortOrder?: string,
  ) {
    return this.pageContentService.getGeneratedAsset({
      siteKey,
      count,
      sortBy,
      sortOrder,
    });
  }

  @Get('assets/video')
  @ApiQuery({
    name: 'siteKey',
    description:
      'Filter by site key - optional if not provided, return all images',
    required: false,
  })
  @ApiQuery({
    name: 'count',
    description: 'Number of images to return',
    required: false,
  })
  @ApiQuery({
    name: 'sortBy',
    description: 'Sort by - possible values: like',
    required: false,
  })
  @ApiQuery({
    name: 'sortOrder',
    description: 'Sort order - possible values: asc, desc',
    required: false,
  })
  async getGeneratedVideo(
    @Query('siteKey') siteKey?: string,
    @Query('count') count: number = 10,
    @Query('sortBy') sortBy?: string,
    @Query('sortOrder') sortOrder?: string,
  ) {
    return this.pageContentService.getGeneratedAssetVideo({
      siteKey,
      count,
      sortBy,
      sortOrder,
    });
  }

  @Get('asset/photobooth/:id')
  @ApiParam({ name: 'id', description: 'Generated asset id' })
  @ApiQuery({
    name: 'photoboothAssetId',
    description: 'Photobooth asset id',
    required: true,
  })
  async getPhotoboothAssetById(
    @Param('id') id: string,
    @Query('photoboothAssetId') photoboothAssetId: string,
  ) {
    return this.pageContentService.getGeneratedAssetInfo(id, photoboothAssetId);
  }

  @Get('asset/:id')
  @ApiParam({ name: 'id', description: 'Generated asset id' })
  async getAssetById(@Param('id') id: string) {
    return this.pageContentService.getGeneratedAssetInfo(id);
  }

  @Post('asset/like')
  @ApiQuery({ name: 'id', description: 'Generated asset id' })
  async likeAsset(@Query('id') id: string) {
    return this.pageContentService.likeAsset(id);
  }

  @Get('badwords')
  async getBadWords() {
    return bad_words;
  }
}
