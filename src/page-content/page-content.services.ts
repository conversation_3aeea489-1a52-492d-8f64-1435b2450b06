import { Injectable } from '@nestjs/common';
import { AssetGeneration, TrackType } from '@prisma/client';
import { PrismaService } from 'nestjs-prisma';
import { GcsService } from 'src/gcs/gcs.service';

@Injectable()
export class PageContentService {
  constructor(
    private prisma: PrismaService,
    private gcsService: GcsService,
  ) {}

  async getPageContent(trackType: TrackType) {
    const sites = await this.prisma.sites.findMany({
      where: { trackType },
      include: { questions: true },
    });

    const personas = await this.prisma.personas.findMany();

    return {
      status: 'success',
      data: { sites, personas },
    };
  }

  async getGeneratedAsset({
    siteKey,
    count,
    sortBy,
    sortOrder,
  }: {
    siteKey?: string;
    count: number;
    sortBy?: string;
    sortOrder?: string;
  }) {
    try {
      // Build the WHERE clause for the count query
      let countWhereClause = `
      WHERE ag.status = 'completed' 
      AND gs.active = true
      AND gs.generatedImageId IS NOT NULL
      AND ag.appId != 'sandbox'`;

      // Add siteKey filter if provided
      if (siteKey) {
        countWhereClause += ` AND JSON_EXTRACT(gs.siteSnapshot, '$.key') = '${siteKey}'`;
      }

      // First, count total available records to handle edge cases
      const totalCount = await this.prisma.$queryRawUnsafe<[{ count: bigint }]>(
        `SELECT COUNT(DISTINCT ag.sessionId) as count 
       FROM asset_generations ag 
       INNER JOIN game_states gs ON ag.sessionId = gs.sessionId 
       ${countWhereClause}`,
      );

      // Convert BigInt to Number for safe comparison
      const totalCountNumber = Number(totalCount[0].count);

      // If no records exist at all, return empty array early
      if (totalCountNumber === 0) {
        return {
          status: 'success',
          error: null,
          data: [],
        };
      }

      // Calculate maximum possible offset to ensure we can still get 'count' records
      const maxOffset = Math.max(0, totalCountNumber - count);

      // Generate a random offset between 0 and maxOffset
      const randomOffset = Math.floor(Math.random() * (maxOffset + 1));

      // Fetch the requested count plus a buffer
      const fetchCount = Math.min(count * 2, totalCountNumber);

      // Build the WHERE clause for the main query
      let whereClause = `
      WHERE ag.status = 'completed' 
      AND gs.active = true
      AND gs.generatedImageId IS NOT NULL 
      AND ag.appId != 'sandbox'
      AND (img.filename IS NULL OR img.filename != '')`;

      // Add siteKey filter if provided
      if (siteKey) {
        whereClause += ` AND JSON_EXTRACT(gs.siteSnapshot, '$.key') = '${siteKey}'`;
      }

      const assetGenerations = await this.prisma.$queryRawUnsafe<
        AssetGeneration[]
      >(
        `SELECT DISTINCT ag.sessionId FROM asset_generations ag 
       INNER JOIN game_states gs ON ag.sessionId = gs.sessionId 
       LEFT JOIN generated_assets img ON img.id = gs.generatedImageId
       ${whereClause}
       ORDER BY RAND() LIMIT ${fetchCount} OFFSET ${randomOffset}`,
      );

      //Build up the query
      const findManyGameStateWhere = {
        sessionId: { in: assetGenerations.map(({ sessionId }) => sessionId) },
      };

      // If siteKey is provided, filter by siteKey
      if (siteKey) {
        findManyGameStateWhere['siteSnapshot'] = {
          path: '$.key',
          equals: siteKey,
        };
      }

      const gameStates = await this.prisma.gameState.findMany({
        where: findManyGameStateWhere,
        select: {
          generatedImageId: true,
          generatedVideoId: true,
          name: true,
          siteSnapshot: true,
          personaSnapshot: true,
        },
      });

      const gameStateMapById = gameStates.reduce((acc, gameState) => {
        acc[gameState.generatedImageId] = gameState;
        return acc;
      }, {});

      const generatedImages = await this.prisma.generatedAsset.findMany({
        where: {
          id: {
            in: gameStates
              .map(({ generatedImageId }) => generatedImageId)
              .filter(Boolean),
          },
        },
        select: {
          id: true,
          sessionId: true,
          filename: true,
          likeCount: true,
          createdAt: true,
        },
      });

      const generatedImagesWithMetadata = await Promise.all(
        generatedImages.map(async (image) => {
          let videoUrl: string | undefined;

          if (gameStateMapById[image.id].generatedVideoId) {
            const video = await this.prisma.generatedAsset.findUnique({
              where: { id: gameStateMapById[image.id].generatedVideoId },
              select: { filename: true },
            });

            if (video && video.filename) {
              videoUrl = await this.gcsService.getFileUrl(video.filename);
            }
          }

          return {
            siteName: gameStateMapById[image.id].siteSnapshot.name,
            siteShortName: gameStateMapById[image.id].siteSnapshot.mapName,
            name: gameStateMapById[image.id].name,
            personaKey: gameStateMapById[image.id].personaSnapshot?.key,
            like: image.likeCount,
            imageId: image.id,
            imageUrl: await this.gcsService.getFileUrl(image.filename),
            videoId: gameStateMapById[image.id].generatedVideoId,
            videoUrl: videoUrl,
            createdAt: image.createdAt,
          };
        }),
      );

      // At the end of the function, before the return statement:
      const finalData = generatedImagesWithMetadata
        .sort((a, b) => {
          if (sortBy === 'like') {
            return sortOrder === 'asc' ? a.like - b.like : b.like - a.like;
          } else if (sortBy === 'createdAt') {
            return sortOrder === 'asc'
              ? a.createdAt.getTime() - b.createdAt.getTime()
              : b.createdAt.getTime() - a.createdAt.getTime();
          }
          return 0;
        })
        .slice(0, count); // Ensure we return exactly the requested count

      return {
        status: 'success',
        error: null,
        data: finalData,
      };
    } catch (error) {
      console.error(error);
      return {
        status: 'error',
        error: 'Failed to get generated images',
        data: null,
      };
    }
  }

  async getGeneratedAssetVideo({
    siteKey,
    count,
    sortBy,
    sortOrder,
  }: {
    siteKey?: string;
    count: number;
    sortBy?: string;
    sortOrder?: string;
  }) {
    try {
      const assetGenerations = await this.prisma.$queryRawUnsafe<
        AssetGeneration[]
      >(
        `SELECT DISTINCT ag.sessionId FROM asset_generations ag 
          INNER JOIN game_states gs ON ag.sessionId = gs.sessionId 
          INNER JOIN generated_assets img ON img.id = gs.generatedImageId
          INNER JOIN generated_assets vid ON vid.id = gs.generatedVideoId
          WHERE ag.status = 'completed' AND ag.appId != 'sandbox' 
          AND gs.active = true
          AND gs.generatedImageId IS NOT NULL AND gs.generatedVideoId IS NOT NULL
          AND img.filename != '' AND vid.filename != ''
          ORDER BY RAND() LIMIT ${count}`,
      );

      //Build up the query
      const findManyGameStateWhere = {
        sessionId: { in: assetGenerations.map(({ sessionId }) => sessionId) },
      };

      // If siteKey is provided, filter by siteKey
      if (siteKey) {
        findManyGameStateWhere['siteSnapshot'] = {
          path: '$.key',
          equals: siteKey,
        };
      }

      const gameStates = await this.prisma.gameState.findMany({
        where: findManyGameStateWhere,
        select: {
          generatedImageId: true,
          generatedVideoId: true,
          name: true,
          siteSnapshot: true,
          personaSnapshot: true,
        },
      });

      const gameStateMapById = gameStates.reduce((acc, gameState) => {
        acc[gameState.generatedImageId] = gameState;
        return acc;
      }, {});

      const generatedImages = await this.prisma.generatedAsset.findMany({
        where: {
          id: {
            in: gameStates
              .map(({ generatedImageId }) => generatedImageId)
              .filter(Boolean),
          },
        },
        select: {
          id: true,
          sessionId: true,
          filename: true,
          likeCount: true,
        },
      });

      const generatedImagesWithMetadata = await Promise.all(
        generatedImages.map(async (image) => {
          const video = await this.prisma.generatedAsset.findUnique({
            where: { id: gameStateMapById[image.id].generatedVideoId },
            select: { filename: true },
          });

          return {
            siteName: gameStateMapById[image.id].siteSnapshot.name,
            name: gameStateMapById[image.id].name,
            personaKey: gameStateMapById[image.id].personaSnapshot?.key,
            like: image.likeCount,
            imageId: image.id,
            imageUrl: await this.gcsService.getFileUrl(image.filename),
            videoId: gameStateMapById[image.id].generatedVideoId,
            videoUrl: await this.gcsService.getFileUrl(video.filename),
          };
        }),
      );

      return {
        status: 'success',
        error: null,
        data: generatedImagesWithMetadata.sort((a, b) => {
          if (sortBy === 'like') {
            return sortOrder === 'asc' ? a.like - b.like : b.like - a.like;
          }
          return 0;
        }),
      };
    } catch (error) {
      console.error(error);
      return {
        status: 'error',
        error: 'Failed to get generated images',
        data: null,
      };
    }
  }

  async getGeneratedAssetInfo(id: string, photoboothAssetId?: string) {
    const result = await this.prisma.$queryRawUnsafe<
      {
        id: string;
        filename: string;
        likeCount: number;
        sessionId: string;
        authorName: string;
        gameStateType: string;
        generatedVideoId: string;
        siteName: string;
        personaKey: string;
        visionStatementTitle: string;
        visionStatement: string;
      }[]
    >(
      `SELECT 
    ga.id, 
    ga.filename, 
    ga.likeCount,
    ga.sessionId,
    gs.name as authorName,
    gs.type as gameStateType,
    gs.generatedVideoId,
    JSON_UNQUOTE(JSON_EXTRACT(gs.siteSnapshot, '$.name')) as siteName,
    JSON_UNQUOTE(JSON_EXTRACT(gs.personaSnapshot, '$.key')) as personaKey,
    CASE 
      WHEN gs.type = 'GROUP' THEN
        CASE 
          WHEN ga.filename LIKE '%-0.png' THEN JSON_UNQUOTE(JSON_EXTRACT(llm.response, '$.what_we_agree_on.vision_statement_title'))
          WHEN ga.filename LIKE '%-1.png' THEN JSON_UNQUOTE(JSON_EXTRACT(llm.response, '$.every_view_counts.vision_statement_title'))
          WHEN ga.filename LIKE '%-2.png' THEN JSON_UNQUOTE(JSON_EXTRACT(llm.response, '$.lets_dream_big.vision_statement_title'))
          ELSE JSON_UNQUOTE(JSON_EXTRACT(llm.response, '$.what_we_agree_on.vision_statement_title'))
        END
      ELSE JSON_UNQUOTE(JSON_EXTRACT(llm.response, '$.vision_statement_title'))
    END as visionStatementTitle,
    CASE 
      WHEN gs.type = 'GROUP' THEN
        CASE 
          WHEN ga.filename LIKE '%-0.png' THEN JSON_UNQUOTE(JSON_EXTRACT(llm.response, '$.what_we_agree_on.vision_statement'))
          WHEN ga.filename LIKE '%-1.png' THEN JSON_UNQUOTE(JSON_EXTRACT(llm.response, '$.every_view_counts.vision_statement'))
          WHEN ga.filename LIKE '%-2.png' THEN JSON_UNQUOTE(JSON_EXTRACT(llm.response, '$.lets_dream_big.vision_statement'))
          ELSE JSON_UNQUOTE(JSON_EXTRACT(llm.response, '$.what_we_agree_on.vision_statement'))
        END
      ELSE JSON_UNQUOTE(JSON_EXTRACT(llm.response, '$.vision_statement'))
    END as visionStatement
  FROM 
    generated_assets ga
  JOIN 
    game_states gs ON ga.sessionId = gs.sessionId
  LEFT JOIN
    llm_generations llm ON ga.sessionId = llm.sessionId
  WHERE 
    ga.id = ?`,
      id,
    );

    if (result.length === 0) {
      return {
        status: 'error',
        error: 'Asset not found',
        data: null,
      };
    }

    const asset = result[0];
    const imageUrl = await this.gcsService.getFileUrl(asset.filename);

    // Get video URL if generatedVideoId exists
    let videoUrl = null;
    if (asset.generatedVideoId) {
      // Get the video asset to get its filename
      const videoAsset = await this.prisma.generatedAsset.findUnique({
        where: { id: asset.generatedVideoId },
        select: { filename: true },
      });

      if (videoAsset && videoAsset.filename) {
        videoUrl = await this.gcsService.getFileUrl(videoAsset.filename);
      }
    }

    // Get comments for this session
    const comments = await this.prisma.generatedComment.findMany({
      where: { sessionId: asset.sessionId },
      select: {
        personaKey: true,
        persona: true,
        comment: true,
      },
    });

    return {
      status: 'success',
      error: null,
      data: {
        id: asset.id,
        likeCount: asset.likeCount,
        authorName: asset.authorName,
        siteName: asset.siteName,
        personaKey: asset.personaKey,
        visionStatementTitle: asset.visionStatementTitle,
        visionStatement: asset.visionStatement,
        comments,
        imageUrl,
        videoUrl,
        photoboothImageUrl: photoboothAssetId
          ? await this.getPhotoboothAssetUrl(photoboothAssetId)
          : null,
      },
    };
  }

  async getPhotoboothAssetUrl(id: string) {
    const asset = await this.prisma.tempGeneratedAsset.findUnique({
      where: { id },
    });

    if (!asset) {
      return null;
    }

    return await this.gcsService.getFileUrl(asset.filename);
  }

  async likeAsset(id: string) {
    await this.prisma.generatedAsset.update({
      where: { id },
      data: { likeCount: { increment: 1 } },
    });

    return { status: 'success', error: null, data: null };
  }
}
