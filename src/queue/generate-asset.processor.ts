import { GoogleGenAI, PersonGeneration } from '@google/genai';
import { InjectQueue, Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { AssetGeneration } from '@prisma/client';
import { Job, Queue } from 'bull';
import { PrismaService } from 'nestjs-prisma';
import { PiAPIService } from 'src/AI/piAPI.service';
import { ReplicateService } from 'src/AI/replicate.service';
import { FluxInpaintInput, SDInpaintInput } from 'src/AI/replicate.types';
import { SocketGateway } from 'src/socket/socket.gateway';

@Processor('generate-asset-queue')
export class GenerateAssetProcessor {
  private readonly project = process.env.GCP_PROJECT_ID;
  private readonly location = process.env.GCP_LOCATION;
  private readonly logger = new Logger(GenerateAssetProcessor.name);

  private readonly googleGenAI: GoogleGenAI = new GoogleGenAI({
    project: this.project,
    location: this.location,
    vertexai: true,
  });

  constructor(
    @InjectQueue('generate-asset-queue')
    private readonly generateAssetQueue: Queue,
    @InjectQueue('generated-asset-queue')
    private readonly generatedAssetQueue: Queue,

    private socketGateway: SocketGateway,

    private readonly prisma: PrismaService,
    private readonly piApiService: PiAPIService,
    private readonly replicateService: ReplicateService,
  ) {}

  @Process({ name: 'generate-asset' })
  async handleGenerateAsset(job: Job<string>) {
    const assetGeneration = await this.prisma.assetGeneration.findUnique({
      where: { id: job.data },
    });

    // Handle low rate-limit provider
    if (['VIDEO_LUMA'].includes(assetGeneration.type)) {
      const processingImageGenerationCount =
        await this.prisma.assetGeneration.count({
          where: {
            status: 'processing',
            type: {
              in: ['VIDEO_LUMA'],
            },
          },
        });

      if (processingImageGenerationCount >= 30) {
        this.generateAssetQueue.add('generate-asset', job.data, {
          delay: 5000,
          priority: 1,
        });
      } else {
        switch (assetGeneration.type) {
          // case 'IMAGE_MIDJOURNEY': {
          //   const newTask = await this.piApiService.createMidjourneyTask(
          //     assetGeneration.prompt,
          //   );

          //   await this.prisma.assetGeneration.update({
          //     where: { id: assetGeneration.id },
          //     data: { taskId: newTask.data.task_id, status: 'processing' },
          //   });
          //   break;
          // }

          // case 'VIDEO_KLING': {
          //   const newTask = await this.piApiService.createKlingTask(
          //     assetGeneration.inputImageUrl,
          //   );

          //   await this.prisma.assetGeneration.update({
          //     where: { id: assetGeneration.id },
          //     data: { taskId: newTask.data.task_id, status: 'processing' },
          //   });
          //   break;
          // }

          case 'VIDEO_LUMA': {
            const newTask = await this.piApiService.createLumaTask(
              assetGeneration.inputImageUrl,
            );

            await this.prisma.assetGeneration.update({
              where: { id: assetGeneration.id },
              data: { taskId: newTask.data.task_id, status: 'processing' },
            });
            break;
          }

          default: {
            this.logger.error('Unknown asset type');
            await this.prisma.assetGeneration.update({
              where: { id: assetGeneration.id },
              data: { status: 'failed' },
            });
            break;
          }
        }
      }
    } else {
      // Handle high rate limit provider
      switch (assetGeneration.type) {
        case 'IMAGE_GEN_4': {
          const updatedAssetGeneration =
            await this.prisma.assetGeneration.update({
              where: { id: assetGeneration.id },
              data: { status: 'processing' },
            });

          const created_at = new Date();

          const response = await this.googleGenAI.models.generateImages({
            model: 'imagen-4.0-generate-preview-06-06',
            prompt: assetGeneration.prompt,
            config: {
              aspectRatio: '16:9',
              numberOfImages: 4,
              personGeneration: PersonGeneration.ALLOW_ALL,
              outputMimeType: 'image/png',
            },
          });

          const ended_at = new Date();

          if (response.generatedImages.length) {
            await this.generatedAssetQueue.add('handle-generated-asset', {
              timestamp: Date.now(),
              data: {
                task_id: updatedAssetGeneration.taskId,
                model: 'imagen-4.0-generate-preview-06-06',
                output: { images: response.generatedImages },
                meta: { created_at, ended_at },
              },
            });
          } else {
            await this.prisma.assetGeneration.update({
              where: { id: assetGeneration.id },
              data: { status: 'failed' },
            });

            this.socketGateway.io
              .to(assetGeneration.sessionId)
              .emit('generated-asset', {
                assetGenerationId: null,
              });
          }

          break;
        }

        case 'IMAGE_FLUX_11_PRO': {
          await this.prisma.assetGeneration.update({
            where: { id: assetGeneration.id },
            data: { status: 'processing' },
          });

          for (let i = 0; i < 4; i++) {
            try {
              this.replicateService.fluxGen(
                {
                  prompt: assetGeneration.prompt,
                  imageGenerationId: assetGeneration.id,
                },
                i,
              );
            } catch (error) {
              this.logger.error('Error generating image', error);

              await this.prisma.assetGeneration.update({
                where: { id: assetGeneration.id },
                data: { status: 'failed' },
              });

              this.socketGateway.io
                .to(assetGeneration.sessionId)
                .emit('generated-asset', {
                  assetGenerationId: null,
                });

              break;
            }
          }

          break;
        }

        case 'IMAGE_FLUX_11_PRO_ULTRA': {
          await this.prisma.assetGeneration.update({
            where: { id: assetGeneration.id },
            data: { status: 'processing' },
          });

          for (let i = 0; i < 4; i++) {
            try {
              this.replicateService.fluxGen(
                {
                  prompt: assetGeneration.prompt,
                  imageGenerationId: assetGeneration.id,
                },
                i,
                true,
              );
            } catch (error) {
              this.logger.error('Error generating image', error);

              await this.prisma.assetGeneration.update({
                where: { id: assetGeneration.id },
                data: { status: 'failed' },
              });

              this.socketGateway.io
                .to(assetGeneration.sessionId)
                .emit('generated-asset', {
                  assetGenerationId: null,
                });

              break;
            }
          }

          break;
        }

        case 'IMAGE_FLUX_KONTEXT_PRO': {
          await this.prisma.assetGeneration.update({
            where: { id: assetGeneration.id },
            data: { status: 'processing' },
          });

          for (let i = 0; i < 4; i++) {
            try {
              await this.replicateService.fluxContext(
                {
                  prompt: assetGeneration.prompt,
                  imageGenerationId: assetGeneration.id,
                },
                i,
              );
            } catch (error) {
              this.logger.error('Error generating image', error);

              await this.prisma.assetGeneration.update({
                where: { id: assetGeneration.id },
                data: { status: 'failed' },
              });

              this.socketGateway.io
                .to(assetGeneration.sessionId)
                .emit('generated-asset', {
                  assetGenerationId: null,
                });
            }
          }

          break;
        }

        default: {
          this.logger.error('Unknown asset type');
          await this.prisma.assetGeneration.update({
            where: { id: assetGeneration.id },
            data: { status: 'failed' },
          });
          break;
        }
      }
    }
  }

  @Process({ name: 'generate-asset-co-create' })
  async handleGenerateAssetCoCreate(job: Job<string>) {
    const assetGeneration = await this.prisma.assetGeneration.findUnique({
      where: { id: job.data },
    });

    if (!assetGeneration.prompts || !Array.isArray(assetGeneration.prompts)) {
      this.logger.error('No prompts found');
      await this.prisma.assetGeneration.update({
        where: { id: assetGeneration.id },
        data: { status: 'failed' },
      });
      return;
    }

    // Handle high rate limit provider
    switch (assetGeneration.type) {
      case 'IMAGE_GEN_4': {
        try {
          const updatedAssetGeneration =
            await this.prisma.assetGeneration.update({
              where: { id: assetGeneration.id },
              data: { status: 'processing' },
            });

          const created_at = new Date();

          const [commonGround, voicesOfAll, boldLeap] = await Promise.all(
            assetGeneration.prompts.map((prompt: string) =>
              this.googleGenAI.models.generateImages({
                model: 'imagen-4.0-generate-preview-06-06',
                prompt,
                config: {
                  aspectRatio: '16:9',
                  numberOfImages: 1,
                  personGeneration: PersonGeneration.ALLOW_ALL,
                  outputMimeType: 'image/png',
                },
              }),
            ),
          );

          const ended_at = new Date();

          if (
            commonGround.generatedImages.length &&
            voicesOfAll.generatedImages.length &&
            boldLeap.generatedImages.length
          ) {
            await this.generatedAssetQueue.add('handle-generated-asset', {
              timestamp: Date.now(),
              data: {
                task_id: updatedAssetGeneration.taskId,
                model: 'imagen-4.0-generate-preview-06-06',
                output: {
                  images: [
                    ...commonGround.generatedImages,
                    ...voicesOfAll.generatedImages,
                    ...boldLeap.generatedImages,
                  ],
                },
                meta: { created_at, ended_at },
              },
            });
          } else {
            await this.prisma.assetGeneration.update({
              where: { id: assetGeneration.id },
              data: { status: 'failed' },
            });

            this.socketGateway.io
              .to(assetGeneration.sessionId)
              .emit('generated-asset', {
                assetGenerationId: null,
              });
          }
        } catch (error) {
          this.logger.error('Error generating image', error);

          await this.prisma.assetGeneration.update({
            where: { id: assetGeneration.id },
            data: { status: 'failed' },
          });

          this.socketGateway.io
            .to(assetGeneration.sessionId)
            .emit('generated-asset', {
              assetGenerationId: null,
            });
        }

        break;
      }

      case 'IMAGE_FLUX_11_PRO': {
        await this.prisma.assetGeneration.update({
          where: { id: assetGeneration.id },
          data: { status: 'processing' },
        });

        for (let i = 0; i < 3; i++) {
          try {
            this.replicateService.fluxGen(
              {
                prompt: <string>assetGeneration.prompts[i],
                imageGenerationId: assetGeneration.id,
              },
              i,
            );
          } catch (error) {
            this.logger.error('Error generating image', error);

            await this.prisma.assetGeneration.update({
              where: { id: assetGeneration.id },
              data: { status: 'failed' },
            });

            this.socketGateway.io
              .to(assetGeneration.sessionId)
              .emit('generated-asset', {
                assetGenerationId: null,
              });

            break;
          }
        }

        break;
      }

      case 'IMAGE_FLUX_11_PRO_ULTRA': {
        await this.prisma.assetGeneration.update({
          where: { id: assetGeneration.id },
          data: { status: 'processing' },
        });

        for (let i = 0; i < 3; i++) {
          try {
            this.replicateService.fluxGen(
              {
                prompt: <string>assetGeneration.prompts[i],
                imageGenerationId: assetGeneration.id,
              },
              i,
              true,
            );
          } catch (error) {
            this.logger.error('Error generating image', error);

            await this.prisma.assetGeneration.update({
              where: { id: assetGeneration.id },
              data: { status: 'failed' },
            });

            this.socketGateway.io
              .to(assetGeneration.sessionId)
              .emit('generated-asset', {
                assetGenerationId: null,
              });

            break;
          }
        }

        break;
      }

      case 'IMAGE_FLUX_KONTEXT_PRO': {
        await this.prisma.assetGeneration.update({
          where: { id: assetGeneration.id },
          data: { status: 'processing' },
        });

        for (let i = 0; i < 3; i++) {
          try {
            this.replicateService.fluxContext(
              {
                prompt: <string>assetGeneration.prompts[i],
                imageGenerationId: assetGeneration.id,
              },
              i,
            );
          } catch (error) {
            this.logger.error('Error generating image', error);

            await this.prisma.assetGeneration.update({
              where: { id: assetGeneration.id },
              data: { status: 'failed' },
            });

            this.socketGateway.io
              .to(assetGeneration.sessionId)
              .emit('generated-asset', {
                assetGenerationId: null,
              });

            break;
          }
        }

        break;
      }

      default: {
        this.logger.error('Unknown asset type');
        await this.prisma.assetGeneration.update({
          where: { id: assetGeneration.id },
          data: { status: 'failed' },
        });
        break;
      }
    }
  }

  @Process({ name: 'inpaint-asset' })
  async handleInpaintAsset(job: Job<string>) {
    const assetGeneration = await this.prisma.assetGeneration.findUnique({
      where: { id: job.data },
    });

    switch (assetGeneration.type) {
      case 'INPAINTING_FLUX': {
        await this.prisma.assetGeneration.update({
          where: { id: assetGeneration.id },
          data: { status: 'processing' },
        });

        for (let i = 0; i < 4; i++) {
          try {
            this.replicateService.fluxInpaint(
              <FluxInpaintInput>assetGeneration.inpaintInput,
              i,
            );
          } catch (error) {
            this.logger.error('Error generating image', error);

            await this.prisma.assetGeneration.update({
              where: { id: assetGeneration.id },
              data: { status: 'failed' },
            });

            this.socketGateway.io
              .to(assetGeneration.sessionId)
              .emit('generated-asset', {
                assetGenerationId: null,
              });

            break;
          }
        }

        break;
      }

      case 'INPAINTING_SD': {
        await this.prisma.assetGeneration.update({
          where: { id: assetGeneration.id },
          data: { status: 'processing' },
        });

        try {
          await this.replicateService.sdInpaint(
            <SDInpaintInput>assetGeneration.inpaintInput,
          );
        } catch (error) {
          this.logger.error('Error generating image', error);

          await this.prisma.assetGeneration.update({
            where: { id: assetGeneration.id },
            data: { status: 'failed' },
          });

          this.socketGateway.io
            .to(assetGeneration.sessionId)
            .emit('generated-asset', {
              assetGenerationId: null,
            });
        }

        break;
      }

      default: {
        this.logger.error('Unknown asset type for inpainting');
        await this.prisma.assetGeneration.update({
          where: { id: assetGeneration.id },
          data: { status: 'failed' },
        });
        break;
      }
    }
  }

  @Process({ name: 'inpaint-co-create-asset' })
  async handleInpaintCoCreateAsset(job: Job<string>) {
    const assetGeneration = await this.prisma.assetGeneration.findUnique({
      where: { id: job.data },
    });

    if (!assetGeneration.prompts || !Array.isArray(assetGeneration.prompts)) {
      this.logger.error('No prompts found');
      await this.prisma.assetGeneration.update({
        where: { id: assetGeneration.id },
        data: { status: 'failed' },
      });
      return;
    }

    switch (assetGeneration.type) {
      case 'INPAINTING_FLUX': {
        await this.prisma.assetGeneration.update({
          where: { id: assetGeneration.id },
          data: { status: 'processing' },
        });

        for (let i = 0; i < 3; i++) {
          try {
            this.replicateService.fluxInpaint(
              {
                ...(<FluxInpaintInput>assetGeneration.inpaintInput),
                prompt: <string>assetGeneration.prompts[i],
              },
              i,
            );
          } catch (error) {
            this.logger.error('Error generating image', error);

            await this.prisma.assetGeneration.update({
              where: { id: assetGeneration.id },
              data: { status: 'failed' },
            });

            this.socketGateway.io
              .to(assetGeneration.sessionId)
              .emit('generated-asset', {
                assetGenerationId: null,
              });

            break;
          }
        }

        break;
      }

      case 'INPAINTING_SD': {
        await this.prisma.assetGeneration.update({
          where: { id: assetGeneration.id },
          data: { status: 'processing' },
        });

        for (let i = 0; i < 3; i++) {
          try {
            this.replicateService.sdInpaint(
              {
                ...(<SDInpaintInput>assetGeneration.inpaintInput),
                prompt: <string>assetGeneration.prompts[i],
              },
              1,
            );
          } catch (error) {
            this.logger.error('Error generating image', error);

            await this.prisma.assetGeneration.update({
              where: { id: assetGeneration.id },
              data: { status: 'failed' },
            });

            this.socketGateway.io
              .to(assetGeneration.sessionId)
              .emit('generated-asset', {
                assetGenerationId: null,
              });

            break;
          }
        }

        break;
      }

      default: {
        this.logger.error('Unknown asset type for inpainting');
        await this.prisma.assetGeneration.update({
          where: { id: assetGeneration.id },
          data: { status: 'failed' },
        });
        break;
      }
    }
  }

  async imageUrlToBase64(url: string): Promise<string> {
    try {
      // Fetch the image
      const response = await fetch(url);
      // Convert response to blob
      const blob = await response.blob();
      // Create a FileReader instance
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result as string);
        reader.onerror = reject;
        // Read the blob as Data URL (Base64)
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      this.logger.error('Error converting image to Base64:', error);
      throw error;
    }
  }
}
