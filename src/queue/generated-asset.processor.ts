import * as ffmpegInstaller from '@ffmpeg-installer/ffmpeg';
import { HttpService } from '@nestjs/axios';
import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { AssetGeneration } from '@prisma/client';
import { Job } from 'bull';
import * as ffmpeg from 'fluent-ffmpeg';
import {
  existsSync,
  mkdirSync,
  readFileSync,
  unlinkSync,
  writeFileSync,
} from 'fs';
import { PrismaService } from 'nestjs-prisma';
import { join } from 'path';
import { firstValueFrom } from 'rxjs';
import * as sharp from 'sharp';
import { AIService } from 'src/AI/AI.service';
import { PiAPIResponse } from 'src/AI/piAPI.types';
import { FluxInpaintOutput, SDInpaintOutput } from 'src/AI/replicate.types';
import { GcsService } from 'src/gcs/gcs.service';
import { SocketGateway } from 'src/socket/socket.gateway';
ffmpeg.setFfmpegPath(ffmpegInstaller.path);

@Processor('generated-asset-queue')
export class GeneratedAssetProcessor {
  private readonly logger = new Logger(GeneratedAssetProcessor.name);

  constructor(
    private prisma: PrismaService,
    private socketGateway: SocketGateway,
    private httpService: HttpService,
    private gcsService: GcsService,
    private aiService: AIService,
  ) {}

  async addWatermark(
    inputPath: string,
    outputPath: string,
    watermarkPath: string,
  ) {
    return new Promise<void>((resolve, reject) => {
      ffmpeg(inputPath)
        .input(watermarkPath)
        .complexFilter([
          {
            filter: 'overlay',
            options: { x: 10, y: 10 }, // top-left corner
          },
        ])
        .on('end', () => resolve())
        .on('error', reject)
        .save(outputPath);
    });
  }

  private async processAndSaveAsset({
    assetBytes,
    mimetype,
    assetUrl,
    index,
    assetGeneration,
    model,
  }: {
    assetBytes?: string;
    mimetype?: string;
    assetUrl?: string;
    index: number;
    assetGeneration: AssetGeneration;
    model: string;
  }): Promise<void> {
    const today = new Date().toISOString().split('T')[0].replace(/-/g, '');
    let buffer: Buffer;
    let _mimeType: string = mimetype;

    if (assetBytes) {
      buffer = Buffer.from(assetBytes, 'base64');
    } else if (assetUrl) {
      const asset = await firstValueFrom(
        this.httpService.get(assetUrl, { responseType: 'arraybuffer' }),
      );
      buffer = Buffer.from(asset.data);
      _mimeType = asset.headers['content-type'];
    } else {
      throw new Error('No assetBytes or assetUrl provided');
    }

    const rawDestination = join(
      assetGeneration.appId,
      today,
      assetGeneration.type,
      assetGeneration.sessionId,
      `raw-${assetGeneration.type}-${index}.${_mimeType.split('/')[1]}`,
    );

    const destination = join(
      assetGeneration.appId,
      today,
      assetGeneration.type,
      assetGeneration.sessionId,
      `${assetGeneration.type}-${index}.${_mimeType.split('/')[1]}`,
    );

    let watermarkedBuffer: Buffer;

    switch (assetGeneration.type) {
      case 'IMAGE_FLUX_11_PRO_ULTRA': {
        const watermarkPath = join(
          process.cwd(),
          'watermarks',
          'flux-ultra-watermark.png',
        );

        watermarkedBuffer = await sharp(buffer)
          .composite([
            {
              input: watermarkPath,
              gravity: 'center',
              blend: 'over',
            },
          ])
          .toBuffer();
        break;
      }

      case 'IMAGE_GEN_4': {
        const watermarkPath = join(
          process.cwd(),
          'watermarks',
          'imagen4-watermark.png',
        );

        watermarkedBuffer = await sharp(buffer)
          .composite([
            {
              input: watermarkPath,
              gravity: 'center',
              blend: 'over',
            },
          ])
          .toBuffer();
        break;
      }

      case 'IMAGE_FLUX_KONTEXT_PRO': {
        const watermarkPath = join(
          process.cwd(),
          'watermarks',
          'kontext-watermark.png',
        );

        watermarkedBuffer = await sharp(buffer)
          .composite([
            {
              input: watermarkPath,
              gravity: 'center',
              blend: 'over',
            },
          ])
          .toBuffer();
        break;
      }

      case 'IMAGE_FLUX_11_PRO': {
        const watermarkPath = join(
          process.cwd(),
          'watermarks',
          'flux-watermark.png',
        );

        watermarkedBuffer = await sharp(buffer)
          .composite([
            {
              input: watermarkPath,
              gravity: 'center',
              blend: 'over',
            },
          ])
          .toBuffer();
        break;
      }

      case 'INPAINTING_FLUX': {
        const watermarkPath = join(
          process.cwd(),
          'watermarks',
          'flux-inpaint-watermark.png',
        );

        watermarkedBuffer = await sharp(buffer)
          .composite([
            {
              input: watermarkPath,
              gravity: 'center',
              blend: 'over',
            },
          ])
          .toBuffer();
        break;
      }

      case 'VIDEO_LUMA': {
        // Write video to temp folder
        const tempDir = join(process.cwd(), 'temp');
        if (!existsSync(tempDir)) {
          mkdirSync(tempDir);
        }
        const inputVideoPath = join(tempDir, `${assetGeneration.id}.mp4`);
        const outputVideoPath = join(
          tempDir,
          `${assetGeneration.id}-watermarked.mp4`,
        );
        writeFileSync(inputVideoPath, buffer);

        const watermarkPath = join(
          process.cwd(),
          'watermarks',
          'luma-watermark.png',
        );

        try {
          await this.addWatermark(
            inputVideoPath,
            outputVideoPath,
            watermarkPath,
          );
          watermarkedBuffer = readFileSync(outputVideoPath);

          // Delete input video
          unlinkSync(inputVideoPath);
          unlinkSync(outputVideoPath);
        } catch (error) {
          this.logger.error('Error adding watermark to video', error);
        }

        break;
      }

      case 'VIDEO_KLING': {
        // Write video to temp folder
        const tempDir = join(process.cwd(), 'temp');
        if (!existsSync(tempDir)) {
          mkdirSync(tempDir);
        }
        const inputVideoPath = join(tempDir, `${assetGeneration.id}.mp4`);
        const outputVideoPath = join(
          tempDir,
          `${assetGeneration.id}-watermarked.mp4`,
        );
        writeFileSync(inputVideoPath, buffer);

        const watermarkPath = join(
          process.cwd(),
          'watermarks',
          'kling-watermark.png',
        );

        try {
          await this.addWatermark(
            inputVideoPath,
            outputVideoPath,
            watermarkPath,
          );
          watermarkedBuffer = readFileSync(outputVideoPath);

          // Delete input video
          unlinkSync(inputVideoPath);
          unlinkSync(outputVideoPath);
        } catch (error) {
          this.logger.error('Error adding watermark to video', error);
        }

        break;
      }

      default:
        break;
    }

    await this.gcsService.uploadBuffer(buffer, rawDestination, _mimeType);
    await this.gcsService.uploadBuffer(
      watermarkedBuffer,
      destination,
      _mimeType,
    );

    // Try to find an exists placeholder for generatedAsset
    const placeholder = await this.prisma.generatedAsset.findFirst({
      where: { assetGenerationId: assetGeneration.id, filename: '' },
    });

    if (placeholder) {
      // This is for real workflow
      await this.prisma.generatedAsset.update({
        where: { id: placeholder.id },
        data: { rawFilename: rawDestination, filename: destination, model },
      });
      return;
    } else {
      // This is for sandbox
      await this.prisma.generatedAsset.create({
        data: {
          appId: assetGeneration.appId,
          sessionId: assetGeneration.sessionId,
          assetGenerationId: assetGeneration.id,
          rawFilename: rawDestination,
          filename: destination,
          model,
        },
      });
    }
  }

  @Process({ name: 'handle-generated-asset' })
  async handleGeneratedAsset(job: Job<PiAPIResponse>) {
    const assetGeneration: AssetGeneration =
      await this.prisma.assetGeneration.findUnique({
        where: { taskId: job.data.data.task_id },
      });

    if (!assetGeneration) {
      console.error(
        `AssetGeneration record not found for taskId: ${job.data.data.task_id}`,
      );
      return;
    }

    try {
      this.logger.log(`Handle generated response type ${assetGeneration.type}`);
      switch (assetGeneration.type) {
        case 'IMAGE_MIDJOURNEY': {
          if ('timestamp' in job.data) {
            await Promise.all(
              (
                job.data.data.output.image_urls ??
                job.data.data.output.temporary_image_urls
              )?.map((assetUrl, index) =>
                this.processAndSaveAsset({
                  assetUrl,
                  index,
                  assetGeneration,
                  model:
                    'timestamp' in job.data ? job.data.data.model : 'unknown',
                }),
              ),
            );
          }
          break;
        }

        case 'IMAGE_GEN_4': {
          await Promise.all(
            job.data.data.output.images.map((generatedImage, index) =>
              this.processAndSaveAsset({
                assetBytes: generatedImage.image.imageBytes,
                mimetype: generatedImage.image.mimeType,
                index,
                assetGeneration,
                model: job.data.data.model ?? 'unknown',
              }),
            ),
          );

          const gameState = await this.prisma.gameState.findUnique({
            where: { sessionId: assetGeneration.sessionId },
            select: { type: true },
          });

          if (gameState.type === 'GROUP') {
            const generatedAssets = await this.prisma.generatedAsset.findMany({
              where: { assetGenerationId: assetGeneration.id },
            });

            generatedAssets.forEach(async (asset) => {
              await this.aiService.generateComments({
                sessionId: assetGeneration.sessionId,
                personaKey: '',
                generatedImageId: asset.id,
              });
            });
          }
          break;
        }

        case 'VIDEO_KLING': {
          await this.processAndSaveAsset({
            assetUrl:
              job.data.data.output.works[0].video.resource_without_watermark,
            index: 0,
            assetGeneration,
            model: job.data.data.model,
          });
        }

        case 'VIDEO_LUMA': {
          await this.processAndSaveAsset({
            assetUrl: job.data.data.output.video_raw.url,
            index: 0,
            assetGeneration,
            model: job.data.data.model,
          });
        }

        default:
          break;
      }

      const duration =
        new Date(job.data.data.meta.ended_at).getTime() -
        new Date(job.data.data.meta.created_at).getTime();

      await this.prisma.assetGeneration.update({
        where: { id: assetGeneration.id },
        data: { status: 'completed', duration },
      });

      this.socketGateway.io
        .to(assetGeneration.sessionId)
        .emit('generated-asset', { assetGenerationId: assetGeneration.id });
    } catch (error) {
      this.logger.error(
        `Error processing job for taskId ${job.data.data.task_id}:`,
        error,
      );
      if (assetGeneration) {
        await this.prisma.assetGeneration.update({
          where: { id: assetGeneration.id },
          data: { status: 'failed' },
        });
      }
      throw error;
    }
  }

  @Process({ name: 'handle-replicate-asset' })
  async handleReplicateFluxAsset(
    job: Job<
      | { type: 'flux'; output: FluxInpaintOutput }
      | { type: 'sd'; output: SDInpaintOutput }
    >,
  ) {
    const assetGeneration = await this.prisma.assetGeneration.findUnique({
      where: { id: job.data.output.input.imageGenerationId },
    });

    switch (job.data.type) {
      case 'flux': {
        await this.processAndSaveAsset({
          assetUrl: job.data.output.output,
          index: job.data.output.input.saveIndex,
          assetGeneration,
          model: job.data.output.model,
        });

        const updatedAssetGeneration =
          await this.prisma.assetGeneration.findUnique({
            where: { id: job.data.output.input.imageGenerationId },
            include: { assetUrls: true },
          });

        const largedGeneratedCreatedAt = Math.max(
          ...updatedAssetGeneration.assetUrls.map((asset) =>
            asset.createdAt.getTime(),
          ),
        );

        await this.prisma.assetGeneration.update({
          where: { id: updatedAssetGeneration.id },
          data: {
            taskId: updatedAssetGeneration.taskId
              ? [updatedAssetGeneration.taskId, job.data.output.id].join(',')
              : job.data.output.id,
            status:
              updatedAssetGeneration.assetUrls.length ===
              assetGeneration.expectedAmount
                ? 'completed'
                : 'processing',
            duration:
              largedGeneratedCreatedAt - assetGeneration.createdAt.getTime(),
          },
        });

        if (
          updatedAssetGeneration.assetUrls.length ===
          assetGeneration.expectedAmount
        ) {
          const gameState = await this.prisma.gameState.findUnique({
            where: { sessionId: assetGeneration.sessionId },
            select: { type: true },
          });

          if (gameState.type === 'GROUP') {
            const generatedAssets = await this.prisma.generatedAsset.findMany({
              where: { assetGenerationId: assetGeneration.id },
            });

            generatedAssets.forEach(async (asset) => {
              await this.aiService.generateComments({
                sessionId: assetGeneration.sessionId,
                personaKey: '',
                generatedImageId: asset.id,
              });
            });
          }

          this.socketGateway.io
            .to(assetGeneration.sessionId)
            .emit('generated-asset', { assetGenerationId: assetGeneration.id });
        }
        break;
      }

      case 'sd': {
        await Promise.all(
          job.data.output.output.map((assetUrl, index) =>
            this.processAndSaveAsset({
              assetUrl,
              index,
              assetGeneration,
              model: job.data.output.model,
            }),
          ),
        );

        const updatedAssetGeneration =
          await this.prisma.assetGeneration.findUnique({
            where: { id: job.data.output.input.imageGenerationId },
            include: { assetUrls: true },
          });

        const largedGeneratedCreatedAt = Math.max(
          ...updatedAssetGeneration.assetUrls.map((asset) =>
            asset.createdAt.getTime(),
          ),
        );

        await this.prisma.assetGeneration.update({
          where: { id: assetGeneration.id },
          data: {
            taskId: updatedAssetGeneration.taskId
              ? [updatedAssetGeneration.taskId, job.data.output.id].join(',')
              : job.data.output.id,
            status:
              updatedAssetGeneration.assetUrls.length ===
              assetGeneration.expectedAmount
                ? 'completed'
                : 'processing',
            duration:
              largedGeneratedCreatedAt - assetGeneration.createdAt.getTime(),
          },
        });

        if (
          updatedAssetGeneration.assetUrls.length ===
          assetGeneration.expectedAmount
        ) {
          const gameState = await this.prisma.gameState.findUnique({
            where: { sessionId: assetGeneration.sessionId },
            select: { type: true },
          });

          if (gameState.type === 'GROUP') {
            const generatedAssets = await this.prisma.generatedAsset.findMany({
              where: { assetGenerationId: assetGeneration.id },
            });

            generatedAssets.forEach(async (asset) => {
              await this.aiService.generateComments({
                sessionId: assetGeneration.sessionId,
                personaKey: '',
                generatedImageId: asset.id,
              });
            });
          }

          this.socketGateway.io
            .to(assetGeneration.sessionId)
            .emit('generated-asset', { assetGenerationId: assetGeneration.id });
        }
        break;
      }
    }
  }
}
