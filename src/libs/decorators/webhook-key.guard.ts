// api-key.guard.ts
import {
  CanActivate,
  ExecutionContext,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Observable } from 'rxjs';

@Injectable()
export class WebhookKeyGuard implements CanActivate {
  private readonly logger = new Logger(WebhookKeyGuard.name);

  constructor(private configService: ConfigService) {}

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const request = context.switchToHttp().getRequest();
    const apiKey = request.headers['x-webhook-secret'];
    const expectedApiKey = this.configService.get<string>(
      'PIAPI_WEBHOOK_SECRET',
    );

    if (!apiKey || apiKey !== expectedApiKey) {
      this.logger.error(
        `Invalid Webhook key from ${request.ip}-${request.headers['x-app-id']}`,
      );
      throw new UnauthorizedException('Invalid Webhook key');
    }
    return true;
  }
}
