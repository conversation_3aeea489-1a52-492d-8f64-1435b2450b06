import {
  CanActivate,
  ExecutionContext,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Observable } from 'rxjs';

@Injectable()
export class AdminKeyGuard implements CanActivate {
  private readonly logger = new Logger(AdminKeyGuard.name);

  constructor(private configService: ConfigService) {}

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const request = context.switchToHttp().getRequest();
    const apiKey = request.headers['x-api-key'];
    const expectedApiKey = this.configService.get<string>('ADMIN_KEY');

    if (!apiKey || apiKey !== expectedApiKey) {
      this.logger.error(
        `Invalid ADMIN key from ${request.ip}-${request.headers['x-app-id']}`,
      );
      throw new UnauthorizedException('Invalid ADMIN key');
    }
    return true;
  }
}
