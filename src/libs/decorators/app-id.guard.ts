import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Observable } from 'rxjs';

@Injectable()
export class AppIdGuard implements CanActivate {
  private readonly logger = new Logger(AppIdGuard.name);

  constructor(private configService: ConfigService) {}

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const request = context.switchToHttp().getRequest();
    const appId = request.headers['x-app-id'];
    const expectedAppIds = this.configService.get<string>('APP_IDS').split(',');

    if (!appId || !expectedAppIds.includes(appId)) {
      this.logger.error(
        `Invalid App ID from ${request.ip}-${request.headers['x-app-id']}`,
      );
      throw new UnauthorizedException('Invalid App ID');
    }
    return true;
  }
}
