# Project 2025 URA Dreamlab Backend

A NestJS-based backend service for handling URA Dreamlab images processing and file management.

## Features

- File upload handling for URA Dreamlab images (JPG, JPEG, PNG)
- Workflow instance management
- API key authentication
- Interaction event tracking
- Integration with external processing service

## Tech Stack

- NestJS v11
- Prisma ORM
- MySQL Database
- Swagger/OpenAPI
- Express.js

## Prerequisites

- Node.js
- MySQL Database
- Environment variables configured

## Installation

```bash
# Install dependencies
npm install

# Sync Prisma schema
npx prisma db push
```

## Environment Variables

Create a `.env` file in the root directory with the following variables:

```env
DATABASE_URL="mysql://user:password@localhost:3306/database_name"
API_KEY="your-api-key"
PORT=3000
```

## Running the Application

```bash
# Development mode
npm run start:dev

# Production mode
npm run build
npm run start:prod
```

## API Documentation

Once the application is running, you can access the Swagger documentation at:

```

## API Endpoints

### Workflow

- `POST /workflow` - Upload ECG file for processing
- `GET /workflow/instance` - Get current workflow instance
- `PUT /workflow/instance` - Update workflow instance

All endpoints require an `x-api-key` header for authentication.

## Project Structure

```
src/
├── libs/           # Shared libraries and utilities
├── workflow/       # Workflow related modules
├── interaction/    # Interaction tracking modules
├── main.module.ts  # Main application module
└── main.ts        # Application entry point
```

## Database Schema

The application uses Prisma with MySQL and includes the following models:

- InteractionEvent - Tracks user interactions
- UploadFile - Stores file upload metadata
- WorkflowInstance - Manages workflow processing instances

## Development

```bash
# Format code
npm run format

# Lint code
npm run lint
```