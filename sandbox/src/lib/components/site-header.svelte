<script lang="ts">
	import { base } from '$app/paths';
	import { page } from '$app/state';
	import { Separator } from '$lib/components/ui/separator/index.js';
	import * as Sidebar from '$lib/components/ui/sidebar/index.js';
</script>

<header
	class="h-(--header-height) group-has-data-[collapsible=icon]/sidebar-wrapper:h-(--header-height) flex shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear"
>
	<div class="flex w-full items-center gap-1 px-4 lg:gap-2 lg:px-6">
		<Sidebar.Trigger class="-ml-1" />
		<Separator orientation="vertical" class="mx-2 data-[orientation=vertical]:h-4" />
		<h1 class="text-base font-medium">
			{#if page.url.pathname === `${base}/`}
				Direct Generation
			{:else if page.url.pathname === `${base}/video`}
				Generate Video
			{:else if page.url.pathname === `${base}/inpaint`}
				Inpaint Image
			{:else if page.url.pathname === `${base}/inpaint-internal`}
				Inpaint Image (Internal Only)
			{:else if page.url.pathname === `${base}/direct-gen-internal`}
				Direct Generation (Internal Only)
			{/if}
		</h1>
	</div>
</header>
