<script lang="ts">
	import * as Sidebar from '$lib/components/ui/sidebar/index.js';
	import CameraIcon from '@tabler/icons-svelte/icons/camera';
	import FileAiIcon from '@tabler/icons-svelte/icons/file-ai';
	import ImageInPictureIcon from '@tabler/icons-svelte/icons/image-in-picture';
	import InnerShadowTopIcon from '@tabler/icons-svelte/icons/inner-shadow-top';
	import VideoIcon from '@tabler/icons-svelte/icons/video';
	import type { ComponentProps } from 'svelte';
	import NavMain from './nav-main.svelte';
	import { base } from '$app/paths';

	const data = {
		navMain: [
			{
				title: 'Direct Generation',
				url: `${base}/`,
				icon: CameraIcon
			},
			{
				title: 'Inpainting Image',
				url: `${base}/inpaint`,
				icon: ImageInPictureIcon
			},
			{
				title: 'Generate Video (Beta)',
				url: `${base}/video`,
				icon: VideoIcon
			},
			{
				title: 'Direct Generation (Internal)',
				url: `${base}/direct-gen-internal`,
				icon: CameraIcon
			},
			{
				title: 'Inpainting Image  (Internal)',
				url: `${base}/inpaint-internal`,
				icon: ImageInPictureIcon
			}
		]
	};

	let { ...restProps }: ComponentProps<typeof Sidebar.Root> = $props();
</script>

<Sidebar.Root collapsible="offcanvas" {...restProps}>
	<Sidebar.Header>
		<Sidebar.Menu>
			<Sidebar.MenuItem>
				<Sidebar.MenuButton class="data-[slot=sidebar-menu-button]:!p-1.5">
					{#snippet child({ props })}
						<a href="##" {...props}>
							<InnerShadowTopIcon class="!size-5" />
							<span class="text-base font-semibold">URA Sandbox</span>
						</a>
					{/snippet}
				</Sidebar.MenuButton>
			</Sidebar.MenuItem>
		</Sidebar.Menu>
	</Sidebar.Header>
	<Sidebar.Content>
		<NavMain items={data.navMain} />
	</Sidebar.Content>
	<Sidebar.Footer></Sidebar.Footer>
</Sidebar.Root>
