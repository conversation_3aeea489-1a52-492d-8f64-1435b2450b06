<script lang="ts">
	import { PUBLIC_API_KEY, PUBLIC_API_URL } from '$env/static/public';
	import * as Select from '$lib/components/ui/select';
	import Button from '@/components/ui/button/button.svelte';
	import Label from '@/components/ui/label/label.svelte';
	import Separator from '@/components/ui/separator/separator.svelte';
	import Textarea from '@/components/ui/textarea/textarea.svelte';
	import { io, Socket } from 'socket.io-client';
	import { onMount } from 'svelte';

	type SessionData = {
		sessionId: string;
	};

	type PageContentData = {
		status: string;
		data: {
			sites: {
				id: string;
				key: string;
				name: string;
				preamble: string;
				tagLine: string;
				keyCharacteristics: string;
				questions: {
					question: string;
					options: { text: string }[];
					mode: 'INDIVIDUAL' | 'GROUP';
				}[];
			}[];
			personas: { id: string; name: string; description: string; dmp: string }[];
		};
	};

	type TextResponseData =
		| {
				status: 'success';
				error: null;
				data: {
					vision_statement: string;
					reasoning: string;
					prompt: string;
					systemPrompt: string;
					systemInstruction: string;
					removedWords: string[];
					duration: number;
				};
		  }
		| {
				status: 'error';
				error: string;
				data: null;
		  };

	type GeneratedImageData = {
		assetGenerationId: string;
	};

	type GeneratedImageResponse = {
		status: string;
		data: {
			duration: number;
			assetUrls: string[];
			prompt: string;
		};
	};

	let error = $state('');
	let generating = $state(false);

	let model = $state<'flux' | 'sd'>('flux');

	let mode = $state<'INDIVIDUAL' | 'GROUP'>('INDIVIDUAL');
	let systemInstructionInput = $state('');
	let sites = $state<PageContentData['data']['sites']>([]);
	let site = $state('');
	let siteKey = $state('');
	let siteTagline = $state('');
	let siteDescription = $state('');
	let siteKeyCharacteristics = $state('');
	let excludedWords = $state('');
	let personas = $state<PageContentData['data']['personas']>([]);
	let persona = $state('');
	let personaDescription = $state('');
	let question1 = $state('');
	let question2 = $state('');
	let answer1 = $state('');
	let answer2 = $state('');

	let prompt = $state('');
	let systemPrompt = $state('');
	let systemInstruction = $state('');
	let visionStatement = $state('');
	let reasoning = $state('');
	let removedWords = $state<string[]>([]);
	let textDuration = $state(0);

	let imageUrls = $state<string[]>([]);
	let imageDuration = $state(0);

	let socket = $state<Socket | null>(null);

	async function generate() {
		generating = true;
		error = '';

		const sessionResponse = await fetch(`${PUBLIC_API_URL}/interactions/start`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'x-api-key': PUBLIC_API_KEY,
				'x-app-id': 'sandbox'
			}
		});

		if (!sessionResponse.ok) {
			generating = false;
			return (error = 'Failed to create session');
		}

		const sessionData: SessionData = await sessionResponse.json();

		const textResponse = await fetch(`${PUBLIC_API_URL}/ai/text/generate`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'x-api-key': PUBLIC_API_KEY
			},
			body: JSON.stringify({
				sessionId: sessionData.sessionId,
				persona,
				personaDescription,
				site,
				siteTagline,
				siteDescription,
				siteKeyCharacteristics,
				q1: question1,
				q2: question2,
				a1: answer1,
				a2: answer2,
				excludedWords,
				systemInstruction: systemInstructionInput
			})
		});

		if (!textResponse.ok) {
			generating = false;
			return (error = 'Failed to generate text');
		}

		const textResponseData: TextResponseData = await textResponse.json();

		if (textResponseData.status === 'error') {
			generating = false;
			return (error = textResponseData.error);
		}

		visionStatement = textResponseData.data.vision_statement;
		reasoning = textResponseData.data.reasoning;
		prompt = textResponseData.data.prompt;
		systemPrompt = textResponseData.data.systemPrompt;
		systemInstruction = textResponseData.data.systemInstruction;
		removedWords = textResponseData.data.removedWords;
		textDuration = textResponseData.data.duration;

		socket = io({
			autoConnect: true,
			transports: ['polling'],
			path: '/api/socket.io',
			auth: { apiKey: PUBLIC_API_KEY, appId: 'sandbox' },
			secure: true
		});

		socket.emit('join-room', { sessionId: sessionData.sessionId });

		const imageResponse = await fetch(`${PUBLIC_API_URL}/ai/image/inpaint/${model}`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'x-api-key': PUBLIC_API_KEY
			},
			body: JSON.stringify({
				sessionId: sessionData.sessionId,
				siteKey,
				prompt
			})
		});

		if (!imageResponse.ok) {
			generating = false;
			return (error = 'Failed to generate image');
		}
	}

	function updatePersonaDescription(persona: string | null) {
		if (persona) {
			const selectedPersona = personas.find((p) => p.name === persona);
			if (selectedPersona) {
				personaDescription = selectedPersona.description;
			} else {
				personaDescription = '';
			}
		} else {
			personaDescription = '';
		}
	}

	function updateSiteDescription(site: string | null) {
		if (site) {
			const selectedSite = sites.find((s) => s.name === site);
			if (selectedSite) {
				siteKey = selectedSite.key;
				siteTagline = selectedSite.tagLine;
				siteDescription = selectedSite.preamble;
				siteKeyCharacteristics = selectedSite.keyCharacteristics;
			} else {
				siteKey = '';
				siteTagline = '';
				siteDescription = '';
				siteKeyCharacteristics = '';
			}
		} else {
			siteKey = '';
			siteTagline = '';
			siteDescription = '';
			siteKeyCharacteristics = '';
		}
	}

	function updateSiteQuestions(mode: 'INDIVIDUAL' | 'GROUP') {
		if (site) {
			const selectedSite = sites.find((s) => s.name === site);
			if (selectedSite) {
				const questions = selectedSite.questions.filter(({ mode: _mode }) => _mode === mode);

				if (mode === 'INDIVIDUAL') {
					question1 = questions[0].question;
				} else {
					question1 = questions[0].question;
					question2 = questions[1].question;
				}
			} else {
				question1 = '';
				question2 = '';
			}
		} else {
			question1 = '';
			question2 = '';
		}
	}

	$effect(() => {
		updatePersonaDescription(persona);
	});

	$effect(() => {
		updateSiteDescription(site);
	});

	$effect(() => {
		updateSiteQuestions(mode);
	});

	$effect(() => {
		if (socket) {
			socket.on('generated-asset', async (data: GeneratedImageData) => {
				const assetResponse = await fetch(
					`${PUBLIC_API_URL}/ai/asset/generate/${data.assetGenerationId}`,
					{ headers: { 'x-api-key': PUBLIC_API_KEY } }
				);

				if (!assetResponse.ok) {
					return (error = 'Failed to get generated image');
				}

				const imageData: GeneratedImageResponse = await assetResponse.json();

				imageUrls = imageData.data.assetUrls;
				imageDuration = imageData.data.duration;
				generating = false;

				socket?.disconnect();
			});
		}
	});

	onMount(async () => {
		const pageContentResponse = await fetch(
			`${PUBLIC_API_URL}/page-content?trackType=INPAINTING_GENERATION`,
			{
				headers: {
					'Content-Type': 'application/json',
					'x-api-key': PUBLIC_API_KEY
				}
			}
		);

		if (!pageContentResponse.ok) {
			generating = false;
			return (error = 'Failed to create session');
		}

		const pageContentData: PageContentData = await pageContentResponse.json();
		sites = pageContentData.data.sites;
		personas = pageContentData.data.personas;
	});
</script>

<div class="flex-1 px-4 lg:px-6">
	<div class="flex h-full gap-4">
		<div class="flex w-1/2 flex-col gap-4">
			<div class="flex flex-col gap-2">
				<Label for="systemInstructionInput">System Instruction</Label>
				<Textarea
					class="min-h-40"
					bind:value={systemInstructionInput}
					id="systemInstructionInput"
				/>
			</div>
			
			<div class="flex flex-col gap-2">
				<Label for="site">Site</Label>
				<Select.Root type="single" bind:value={site} name="site">
					<Select.Trigger class="w-full">
						{site ? sites.find((s) => s.name === site)?.name : 'Select Site'}
					</Select.Trigger>
					<Select.Content>
						{#each sites as s}
							<Select.Item value={s.name}>{s.name}</Select.Item>
						{/each}
					</Select.Content>
				</Select.Root>
			</div>

			<div class="flex flex-col gap-2">
				<Label for="siteTagline">Site Tag Line</Label>
				<Textarea class="min-h-20" bind:value={siteTagline} id="siteTagline" />
			</div>

			<div class="flex flex-col gap-2">
				<Label for="siteDescription">Site Preamble</Label>
				<Textarea class="min-h-20" bind:value={siteDescription} id="siteDescription" />
			</div>

			<div class="flex flex-col gap-2">
				<Label class="font-bold text-red-400" for="siteKeyCharacteristics">
					Site Key Characteristics (For OICs’ inputs)
				</Label>
				<Textarea
					class="min-h-20"
					bind:value={siteKeyCharacteristics}
					id="siteKeyCharacteristics"
				/>
			</div>

			<div class="flex flex-col gap-2">
				<Label class="font-bold text-red-400" for="excludedWords">
					Excluded Words (For OICs’ inputs)
				</Label>
				<Textarea class="min-h-20" bind:value={excludedWords} id="excludedWords" />
			</div>

			<div class="flex flex-col gap-2">
				<Label for="persona">Persona</Label>
				<Select.Root type="single" bind:value={persona} name="persona">
					<Select.Trigger class="w-full">
						{persona ? persona : 'Select Persona'}
					</Select.Trigger>
					<Select.Content>
						{#each personas as p}
							<Select.Item value={p.name}>{p.name}</Select.Item>
						{/each}
					</Select.Content>
				</Select.Root>
			</div>

			<div class="flex flex-col gap-2">
				<Label for="personaDescription">Persona Description</Label>
				<Textarea class="min-h-20" bind:value={personaDescription} id="personaDescription" />
			</div>

			{#if question1}
				<div class="flex flex-col gap-2">
					<Label for="question1">Question 1</Label>
					<Textarea class="min-h-20" bind:value={question1} id="question1" />
				</div>

				<div class="flex flex-col gap-2">
					<Label for="answer1">Answer 1</Label>
					<Textarea class="min-h-20" bind:value={answer1} id="answer1" />
				</div>
			{/if}

			{#if question2}
				<div class="flex flex-col gap-2">
					<Label for="question2">Question 2</Label>
					<Textarea class="min-h-20" bind:value={question2} id="question2" />
				</div>

				<div class="flex flex-col gap-2">
					<Label for="answer2">Answer 2</Label>
					<Textarea class="min-h-20" bind:value={answer2} id="answer2" />
				</div>
			{/if}

			<Button
				class="disabled:cursor-not-allowed disabled:opacity-50"
				onclick={generate}
				disabled={generating}
			>
				{generating ? 'Generating...' : 'Generate'}
			</Button>
		</div>

		<Separator orientation="vertical" />

		<div class="flex w-1/2 flex-col gap-4">
			{#if error}
				<p class="text-red-500">{error}</p>
			{:else if visionStatement && reasoning}
				{#if imageDuration}
					<p class="text-sm text-gray-500">
						Inpainting time taken: {(imageDuration / 1000).toFixed(2)} seconds
					</p>
				{/if}

				{#if imageUrls.length > 0}
					<div class="grid grid-cols-2 gap-4">
						{#each imageUrls as imageUrl}
							<div class="relative flex items-center justify-center">
								<img src={imageUrl} alt="Generated" />
								<Button
									class="absolute bottom-2 right-2 bg-white text-black"
									onclick={() => navigator.clipboard.writeText(imageUrl)}
								>
									Copy URL
								</Button>
							</div>
						{/each}
					</div>
				{/if}

				{#if textDuration}
					<p class="text-sm text-gray-500">
						TextGen Time taken: {(textDuration / 1000).toFixed(2)} seconds
					</p>
				{/if}

				<div class="flex flex-col gap-2">
					<p class="text-xl font-medium">Vision Statement</p>
					<p class="text-sm">
						{@html visionStatement}
					</p>
				</div>

				<div class="flex flex-col gap-2">
					<p class="text-xl font-medium">Reasoning</p>
					<div class="text-sm [&>li]:list-decimal [&>li]:pl-4 [&>ol]:list-decimal [&>ol]:pl-4">
						{@html reasoning}
					</div>
				</div>

				<div class="flex flex-col gap-2">
					<p class="text-xl font-medium">Image Prompt</p>
					<p class="text-sm">{prompt}</p>
				</div>

				<div class="flex flex-col gap-2">
					<p class="text-xl font-medium">System Prompt</p>
					<p class="text-sm">{systemPrompt}</p>
				</div>

				<div class="flex flex-col gap-2">
					<p class="text-xl font-medium">System Instruction</p>
					<p class="text-sm">{systemInstruction}</p>
				</div>

				<div class="flex flex-col gap-2">
					<p class="text-xl font-medium">Removed Words</p>
					<p class="text-sm">{removedWords.join(', ')}</p>
				</div>
			{/if}
		</div>
	</div>
</div>
