<script lang="ts">
	import { PUBLIC_API_KEY, PUBLIC_API_URL } from '$env/static/public';
	import Button from '@/components/ui/button/button.svelte';
	import Label from '@/components/ui/label/label.svelte';
	import Textarea from '@/components/ui/textarea/textarea.svelte';
	import { io, Socket } from 'socket.io-client';

	type SessionData = {
		sessionId: string;
	};

	type GeneratedVideoData = {
		assetGenerationId: string;
	};

	type GeneratedVideoResponse = {
		status: string;
		data: {
			duration: number;
			assetUrls: string[];
		};
	};

	let error = $state('');
	let generating = $state(false);

	let imageUrl = $state('');
	let videoUrl = $state<string>('');
	let duration = $state(0);

	let socket = $state<Socket | null>(null);

	async function generate() {
		generating = true;
		error = '';

		const sessionResponse = await fetch(`${PUBLIC_API_URL}/interactions/start`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'x-api-key': PUBLIC_API_KEY,
				'x-app-id': 'sandbox'
			}
		});

		if (!sessionResponse.ok) {
			generating = false;
			return (error = 'Failed to create session');
		}

		const sessionData: SessionData = await sessionResponse.json();

		socket = io({
			autoConnect: true,
			transports: ['polling'],
			path: '/api/socket.io',
			auth: { apiKey: PUBLIC_API_KEY, appId: 'sandbox' },
			secure: true
		});

		socket.emit('join-room', { sessionId: sessionData.sessionId });

		const response = await fetch(`${PUBLIC_API_URL}/ai/video/generate`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'x-api-key': PUBLIC_API_KEY
			},
			body: JSON.stringify({ sessionId: sessionData.sessionId, imageUrl })
		});

		if (!response.ok) {
			generating = false;
			return (error = 'Failed to generate image');
		}
	}

	$effect(() => {
		if (socket) {
			socket.on('generated-asset', async (data: GeneratedVideoData) => {
				const assetResponse = await fetch(
					`${PUBLIC_API_URL}/ai/asset/generate/${data.assetGenerationId}`,
					{ headers: { 'x-api-key': PUBLIC_API_KEY } }
				);

				if (!assetResponse.ok) {
					return (error = 'Failed to get generated image');
				}

				const videoData: GeneratedVideoResponse = await assetResponse.json();

				videoUrl = videoData.data.assetUrls[0];
				duration = videoData.data.duration;
				generating = false;

				socket?.disconnect();
			});
		}
	});
</script>

<div class="flex-1 px-4 lg:px-6">
	<div class="flex flex-col gap-4">
		<div class="flex flex-col gap-2">
			<Label for="imageUrl">Image Url</Label>
			<Textarea class="min-h-20" bind:value={imageUrl} id="imageUrl" />
		</div>

		<Button
			class="disabled:cursor-not-allowed disabled:opacity-50"
			onclick={generate}
			disabled={generating}
		>
			{generating ? 'Generating...' : 'Generate'}
		</Button>

		{#if error}
			<p class="text-red-500">{error}</p>
		{/if}

		{#if duration}
			<p class="text-sm text-gray-500">
				Time taken: {(duration / 1000).toFixed(2)} seconds
			</p>
		{/if}

		{#if videoUrl}
			<video class="aspect-video w-full" src={videoUrl} muted loop autoplay controls>
				<track kind="captions" />
			</video>
		{/if}
	</div>
</div>
