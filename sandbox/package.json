{"name": "sandbox", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "deploy": "npm run build && pm2 restart sandbox-ui && pm2 save"}, "devDependencies": {"@dnd-kit-svelte/core": "^0.0.8", "@dnd-kit-svelte/modifiers": "^0.0.8", "@dnd-kit-svelte/sortable": "^0.0.8", "@dnd-kit-svelte/utilities": "^0.0.8", "@eslint/compat": "^1.2.9", "@eslint/js": "^9.27.0", "@internationalized/date": "^3.8.1", "@lucide/svelte": "^0.482.0", "@sveltejs/kit": "^2.21.1", "@sveltejs/vite-plugin-svelte": "^5.0.3", "@tabler/icons-svelte": "^3.33.0", "@tailwindcss/vite": "^4.1.7", "@tanstack/table-core": "^8.21.3", "@types/d3-scale": "^4.0.9", "@types/d3-shape": "^3.1.7", "bits-ui": "^2.4.1", "clsx": "^2.1.1", "d3-scale": "^4.0.2", "d3-shape": "^3.2.0", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-svelte": "^3.9.0", "globals": "^16.2.0", "layerchart": "^2.0.0-next.10", "prettier": "^3.5.3", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.11", "socket.io-client": "^4.8.1", "svelte": "^5.33.3", "svelte-check": "^4.2.1", "svelte-sonner": "^1.0.3", "tailwind-merge": "^3.3.0", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.1.7", "tw-animate-css": "^1.3.0", "typescript": "^5.8.3", "typescript-eslint": "^8.32.1", "vaul-svelte": "^1.0.0-next.7", "vite": "^6.3.5", "zod": "^3.25.30"}, "dependencies": {"@sveltejs/adapter-static": "^3.0.8"}}