{"name": "project-2025-uradreamlab-backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "pm2 start dist/main.js --name=interaction-server", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "deploy": "npm run build && pm2 restart api && pm2 save"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "dependencies": {"@ffmpeg-installer/ffmpeg": "^1.1.0", "@google-cloud/storage": "^7.16.0", "@google-cloud/vertexai": "^1.10.0", "@google/genai": "^1.5.1", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^4.0.0", "@nestjs/bull": "^11.0.2", "@nestjs/common": "^11.1.3", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.3", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "*", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.3", "@nestjs/platform-socket.io": "^11.1.3", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.2.0", "@nestjs/websockets": "^11.1.3", "@prisma/client": "^6.10.1", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "bull": "^4.16.5", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "fluent-ffmpeg": "^2.1.3", "nestjs-prisma": "^0.25.0", "nodemailer": "^7.0.3", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "reflect-metadata": "^0.2.2", "replicate": "^1.0.1", "rxjs": "^7.8.2", "sharp": "^0.34.2", "uuid": "^11.1.0"}, "devDependencies": {"@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@types/bcryptjs": "^2.4.6", "@types/express": "^5.0.3", "@types/multer": "^1.4.13", "@types/node": "^24.0.3", "@types/nodemailer": "^6.4.17", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.0", "prettier": "^3.5.3", "prisma": "^6.10.1", "source-map-support": "^0.5.21", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "overrides": {"@nestjs-modules/mailer": {"mjml": "^5.0.0-alpha.6"}}}