// place files you want to import through the `$lib` alias in this folder.

// HTTP Client exports
export {
	HttpClient,
	apiClient,
	get,
	post,
	put,
	patch,
	del as delete,
	getData,
	postData,
	putData,
	patchData,
	deleteData,
	ApiClientError,
	NetworkError,
	TimeoutError
} from './api.js';

// API Services exports
export {
	authService,
	reviewService,
	userService,
	fileService,
	analyticsService,
	systemService,
	handleApiError,
	buildQueryParams,
	createCustomApiClient
} from './services/api.js';

// Type exports
export type {
	ApiConfig,
	RequestOptions,
	ApiResponse,
	ApiError
} from './api.js';

export type {
	ApiResponseWrapper,
	PaginationMeta,
	ApiErrorResponse,
	LoginRequest,
	LoginResponse,
	RefreshTokenRequest,
	RefreshTokenResponse,
	User,
	ReviewItem,
	ReviewStatus,
	ReviewPriority,
	CreateReviewRequest,
	UpdateReviewRequest,
	ReviewListQuery,
	ReviewListResponse,
	UserListQuery,
	UserListResponse,
	CreateUserRequest,
	UpdateUserRequest,
	FileUploadResponse,
	BulkFileUploadResponse,
	AnalyticsQuery,
	AnalyticsResponse,
	HealthCheckResponse,
	SystemConfigResponse
} from './types/api.js';
