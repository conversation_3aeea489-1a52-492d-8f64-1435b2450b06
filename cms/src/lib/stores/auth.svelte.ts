import { getContext, setContext } from 'svelte';
import { goto } from '$app/navigation';
import { browser } from '$app/environment';
import { UserRole } from '$lib/types/auth';
import { authService, handleApiError } from '$lib/services/api';
import { ApiClientError } from '$lib/api';

// Types for authentication system
export interface User {
	id: string;
	name: string;
	email: string;
	avatar?: string;
	role?: UserRole;
}

export interface AuthState {
	user: User | null;
	isAuthenticated: boolean;
	isLoading: boolean;
	error: string | null;
}

// Authentication Store Class
class AuthStore {
	// Core state using Svelte 5 runes
	private _user = $state<User | null>(null);
	private _isLoading = $state<boolean>(true);
	private _error = $state<string | null>(null);

	// Computed values
	user = $derived(this._user);
	isAuthenticated = $derived(this._user !== null);
	isLoading = $derived(this._isLoading);
	error = $derived(this._error);

	constructor() {
		// Initialize auth state from localStorage on client side
		if (browser) {
			this.initializeAuth();
		}
	}

	// Initialize authentication state from stored data
	private initializeAuth(): void {
		try {
			const storedUser = localStorage.getItem('auth_user');
			const storedToken = localStorage.getItem('auth_token');

			if (storedUser && storedToken) {
				this._user = JSON.parse(storedUser);
			}
		} catch (error) {
			console.error('Failed to initialize auth:', error);
			this.clearAuth();
		} finally {
			this._isLoading = false;
		}
	}

	// Login method
	async login(email: string, password: string): Promise<boolean> {
		this._isLoading = true;
		this._error = null;

		try {
			// Use the new API service for authentication
			const loginResult = await authService.login({ email, password });

			// Update user state
			this._user = loginResult.user;

			// Store auth data (the API client handles token storage automatically)
			if (browser) {
				localStorage.setItem('auth_user', JSON.stringify(loginResult.user));
			}

			return true;
		} catch (error) {
			// Handle API errors with proper error extraction
			if (error instanceof ApiClientError) {
				this._error = error.message;
			} else {
				this._error = handleApiError(error);
			}
			return false;
		} finally {
			this._isLoading = false;
		}
	}

	// Logout method
	async logout(): Promise<void> {
		try {
			// Call API logout service (this also clears the auth token)
			await authService.logout();
		} catch (error) {
			// Even if logout fails on server, we still clear local state
			console.warn('Logout API call failed:', error);
		} finally {
			// Clear local state
			this._user = null;
			this._error = null;

			// Clear stored auth data (auth service already cleared token)
			if (browser) {
				localStorage.removeItem('auth_user');
			}

			// Redirect to login
			goto('/login');
		}
	}

	// Clear authentication data
	private clearAuth(): void {
		this._user = null;
		this._error = null;

		if (browser) {
			localStorage.removeItem('auth_user');
			localStorage.removeItem('auth_token');
		}
	}

	// Set error message
	setError(error: string | null): void {
		this._error = error;
	}

	// Clear error message
	clearError(): void {
		this._error = null;
	}

	// Check if user has specific role
	hasRole(role: UserRole): boolean {
		return this._user?.role === role;
	}

	// Check if user has any of the specified roles
	hasAnyRole(roles: UserRole[]): boolean {
		if (!this._user?.role) return false;
		return roles.includes(this._user.role);
	}

	// Update user information
	updateUser(updates: Partial<User>): void {
		if (this._user) {
			this._user = { ...this._user, ...updates };

			// Update stored user data
			if (browser) {
				localStorage.setItem('auth_user', JSON.stringify(this._user));
			}
		}
	}

	// Refresh authentication (e.g., validate token with server)
	async refresh(): Promise<boolean> {
		if (!browser) return false;

		try {
			this._isLoading = true;

			// Get current user profile to validate token
			const user = await authService.getProfile();

			// Update user state with fresh data
			this._user = user;

			// Update stored user data
			localStorage.setItem('auth_user', JSON.stringify(user));

			return true;
		} catch (error) {
			console.error('Token refresh failed:', error);
			this.clearAuth();
			return false;
		} finally {
			this._isLoading = false;
		}
	}
}

// Context management
const AUTH_CONTEXT_KEY = Symbol('auth');

export function setAuthStore(): AuthStore {
	const store = new AuthStore();
	setContext(AUTH_CONTEXT_KEY, store);
	return store;
}

export function getAuthStore(): AuthStore {
	const store = getContext<AuthStore>(AUTH_CONTEXT_KEY);
	if (!store) {
		throw new Error(
			'Auth store not found. Make sure to call setAuthStore() in a parent component.'
		);
	}
	return store;
}

// Export the store class for direct usage
export { AuthStore };

// Helper function to create auth store without context
export function createAuthStore(): AuthStore {
	return new AuthStore();
}
