<!--
  Example component demonstrating the HTTP client usage
  This shows various patterns for using the API client in Svelte components
-->
<script lang="ts">
	import { reviewService, userService, handleApiError } from '$lib/services/api';
	import { apiClient } from '$lib/api';
	import type { ReviewItem, User } from '$lib/types/api';
	import { ReviewStatus, ReviewPriority } from '$lib/types/api';
	
	// State management using Svelte 5 runes
	let reviews: ReviewItem[] = $state([]);
	let users: User[] = $state([]);
	let loading = $state(false);
	let error = $state<string | null>(null);
	let selectedStatus: ReviewStatus = $state(ReviewStatus.PENDING);
	
	// Example 1: Load data on component mount
	$effect(() => {
		loadInitialData();
	});
	
	async function loadInitialData() {
		loading = true;
		error = null;
		
		try {
			// Load reviews and users in parallel
			const [reviewsResult, usersResult] = await Promise.all([
				reviewService.getReviews({ 
					status: selectedStatus, 
					limit: 10 
				}),
				userService.getUsers({ 
					limit: 20 
				})
			]);
			
			reviews = reviewsResult.items;
			users = usersResult.items;
		} catch (err) {
			error = handleApiError(err);
		} finally {
			loading = false;
		}
	}
	
	// Example 2: Filter reviews by status
	async function filterReviews(status: ReviewStatus) {
		selectedStatus = status;
		loading = true;
		error = null;
		
		try {
			const result = await reviewService.getReviews({
				status,
				limit: 10
			});
			reviews = result.items;
		} catch (err) {
			error = handleApiError(err);
		} finally {
			loading = false;
		}
	}
	
	// Example 3: Create a new review
	async function createReview() {
		try {
			const newReview = await reviewService.createReview({
				title: 'New Review',
				description: 'Created from example component',
				priority: ReviewPriority.MEDIUM
			});
			
			// Add to local state
			reviews = [newReview, ...reviews];
		} catch (err) {
			error = handleApiError(err);
		}
	}
	
	// Example 4: Update review status
	async function updateReviewStatus(reviewId: string, status: ReviewStatus) {
		try {
			const updatedReview = await reviewService.updateReview(reviewId, { status });
			
			// Update local state
			reviews = reviews.map(review => 
				review.id === reviewId ? updatedReview : review
			);
		} catch (err) {
			error = handleApiError(err);
		}
	}
	
	// Example 5: Delete a review
	async function deleteReview(reviewId: string) {
		if (!confirm('Are you sure you want to delete this review?')) return;
		
		try {
			await reviewService.deleteReview(reviewId);
			
			// Remove from local state
			reviews = reviews.filter(review => review.id !== reviewId);
		} catch (err) {
			error = handleApiError(err);
		}
	}
	
	// Example 6: Check authentication status
	const isAuthenticated = $derived(apiClient.isAuthenticated());
	
	// Example 7: Raw API client usage (for custom endpoints)
	async function customApiCall() {
		try {
			// Using the raw client for custom endpoints
			const customData = await apiClient.getData('/custom-endpoint');
			console.log('Custom data:', customData);
			
			// With custom headers
			const protectedData = await apiClient.getData('/protected-endpoint', {
				headers: {
					'X-Custom-Header': 'custom-value'
				}
			});
			console.log('Protected data:', protectedData);
		} catch (err) {
			error = handleApiError(err);
		}
	}
	
	// Example 8: File upload
	async function handleFileUpload(event: Event) {
		const input = event.target as HTMLInputElement;
		const file = input.files?.[0];
		
		if (!file) return;
		
		try {
			loading = true;
			
			// Import file service dynamically if needed
			const { fileService } = await import('$lib/services/api');
			const result = await fileService.uploadFile(file);
			
			console.log('File uploaded:', result.url);
		} catch (err) {
			error = handleApiError(err);
		} finally {
			loading = false;
		}
	}
</script>

<div class="api-example">
	<h2>API Client Example</h2>
	
	<!-- Authentication Status -->
	<div class="auth-status">
		<p>Authentication Status: 
			<span class={isAuthenticated ? 'authenticated' : 'unauthenticated'}>
				{isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
			</span>
		</p>
	</div>
	
	<!-- Error Display -->
	{#if error}
		<div class="error">
			<p>Error: {error}</p>
			<button onclick={() => error = null}>Dismiss</button>
		</div>
	{/if}
	
	<!-- Loading State -->
	{#if loading}
		<div class="loading">
			<p>Loading...</p>
		</div>
	{/if}
	
	<!-- Controls -->
	<div class="controls">
		<button onclick={createReview}>Create Review</button>
		<button onclick={customApiCall}>Custom API Call</button>
		
		<select bind:value={selectedStatus} onchange={() => filterReviews(selectedStatus)}>
			<option value={ReviewStatus.PENDING}>Pending</option>
			<option value={ReviewStatus.IN_PROGRESS}>In Progress</option>
			<option value={ReviewStatus.COMPLETED}>Completed</option>
			<option value={ReviewStatus.REJECTED}>Rejected</option>
		</select>
		
		<input type="file" onchange={handleFileUpload} />
	</div>
	
	<!-- Reviews List -->
	<div class="reviews">
		<h3>Reviews ({reviews.length})</h3>
		{#each reviews as review (review.id)}
			<div class="review-item">
				<h4>{review.title}</h4>
				<p>Status: {review.status}</p>
				<p>Priority: {review.priority}</p>
				{#if review.description}
					<p>{review.description}</p>
				{/if}
				
				<div class="review-actions">
					<button onclick={() => updateReviewStatus(review.id, ReviewStatus.COMPLETED)}>
						Mark Complete
					</button>
					<button onclick={() => updateReviewStatus(review.id, ReviewStatus.IN_PROGRESS)}>
						Mark In Progress
					</button>
					<button onclick={() => deleteReview(review.id)} class="danger">
						Delete
					</button>
				</div>
			</div>
		{/each}
		
		{#if reviews.length === 0 && !loading}
			<p>No reviews found.</p>
		{/if}
	</div>
	
	<!-- Users List -->
	<div class="users">
		<h3>Users ({users.length})</h3>
		{#each users as user (user.id)}
			<div class="user-item">
				<p><strong>{user.name}</strong> ({user.email})</p>
				<p>Role: {user.role}</p>
			</div>
		{/each}
	</div>
</div>

<style>
	.api-example {
		max-width: 800px;
		margin: 0 auto;
		padding: 20px;
	}
	
	.auth-status {
		margin-bottom: 20px;
		padding: 10px;
		border: 1px solid #ddd;
		border-radius: 4px;
	}
	
	.authenticated {
		color: green;
		font-weight: bold;
	}
	
	.unauthenticated {
		color: red;
		font-weight: bold;
	}
	
	.error {
		background-color: #fee;
		border: 1px solid #fcc;
		padding: 10px;
		margin-bottom: 20px;
		border-radius: 4px;
	}
	
	.loading {
		text-align: center;
		padding: 20px;
		font-style: italic;
	}
	
	.controls {
		display: flex;
		gap: 10px;
		margin-bottom: 20px;
		flex-wrap: wrap;
	}
	
	.controls button, .controls select, .controls input {
		padding: 8px 12px;
		border: 1px solid #ddd;
		border-radius: 4px;
	}
	
	.controls button:hover {
		background-color: #f5f5f5;
	}
	
	.reviews, .users {
		margin-bottom: 30px;
	}
	
	.review-item, .user-item {
		border: 1px solid #ddd;
		padding: 15px;
		margin-bottom: 10px;
		border-radius: 4px;
	}
	
	.review-actions {
		display: flex;
		gap: 10px;
		margin-top: 10px;
	}
	
	.review-actions button {
		padding: 5px 10px;
		border: 1px solid #ddd;
		border-radius: 3px;
		font-size: 12px;
	}
	
	.danger {
		background-color: #fee;
		border-color: #fcc;
	}
	
	.danger:hover {
		background-color: #fdd;
	}
</style>
