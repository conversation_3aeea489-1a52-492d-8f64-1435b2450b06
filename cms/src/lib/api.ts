/**
 * Centralized HTTP Client for API interactions
 * 
 * Features:
 * - Centralized configuration management
 * - Standardized error handling
 * - Automatic data formatting (JSON)
 * - Extensible headers (auth tokens, etc.)
 * - Strong TypeScript typing
 * - SvelteKit compatibility
 */

import { browser } from '$app/environment';

// ============================================================================
// Configuration
// ============================================================================

export interface ApiConfig {
	baseUrl: string;
	defaultHeaders: Record<string, string>;
	timeout: number;
}

const DEFAULT_CONFIG: ApiConfig = {
	baseUrl: '/api', // Adjust this to your API base URL
	defaultHeaders: {
		'Content-Type': 'application/json',
		'Accept': 'application/json',
	},
	timeout: 10000, // 10 seconds
};

// ============================================================================
// Types
// ============================================================================

export interface ApiError {
	message: string;
	status: number;
	statusText: string;
	data?: unknown;
}

export interface RequestOptions {
	headers?: Record<string, string>;
	timeout?: number;
	signal?: AbortSignal;
	// Allow custom fetch instance (useful for SvelteKit load functions)
	fetch?: typeof fetch;
}

export interface ApiResponse<T = unknown> {
	data: T;
	status: number;
	statusText: string;
	headers: Headers;
}

// ============================================================================
// Error Classes
// ============================================================================

export class ApiClientError extends Error implements ApiError {
	public readonly status: number;
	public readonly statusText: string;
	public readonly data?: unknown;

	constructor(message: string, status: number, statusText: string, data?: unknown) {
		super(message);
		this.name = 'ApiClientError';
		this.status = status;
		this.statusText = statusText;
		this.data = data;
	}
}

export class NetworkError extends Error {
	constructor(message: string, public readonly originalError: Error) {
		super(message);
		this.name = 'NetworkError';
	}
}

export class TimeoutError extends Error {
	constructor(timeout: number) {
		super(`Request timed out after ${timeout}ms`);
		this.name = 'TimeoutError';
	}
}

// ============================================================================
// HTTP Client Class
// ============================================================================

export class HttpClient {
	private config: ApiConfig;
	private authToken: string | null = null;

	constructor(config: Partial<ApiConfig> = {}) {
		this.config = { ...DEFAULT_CONFIG, ...config };
		
		// Initialize auth token from localStorage if available
		if (browser) {
			this.authToken = localStorage.getItem('auth_token');
		}
	}

	// ========================================================================
	// Configuration Methods
	// ========================================================================

	/**
	 * Update the base URL
	 */
	setBaseUrl(baseUrl: string): void {
		this.config.baseUrl = baseUrl;
	}

	/**
	 * Set authentication token
	 */
	setAuthToken(token: string | null): void {
		this.authToken = token;
		if (browser) {
			if (token) {
				localStorage.setItem('auth_token', token);
			} else {
				localStorage.removeItem('auth_token');
			}
		}
	}

	/**
	 * Get current auth token
	 */
	getAuthToken(): string | null {
		return this.authToken;
	}

	/**
	 * Update default headers
	 */
	setDefaultHeaders(headers: Record<string, string>): void {
		this.config.defaultHeaders = { ...this.config.defaultHeaders, ...headers };
	}

	// ========================================================================
	// Private Helper Methods
	// ========================================================================

	/**
	 * Build complete URL from endpoint
	 */
	private buildUrl(endpoint: string): string {
		// Handle absolute URLs
		if (endpoint.startsWith('http://') || endpoint.startsWith('https://')) {
			return endpoint;
		}

		// Handle relative URLs
		const baseUrl = this.config.baseUrl.endsWith('/') 
			? this.config.baseUrl.slice(0, -1) 
			: this.config.baseUrl;
		
		const path = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
		
		return `${baseUrl}${path}`;
	}

	/**
	 * Build request headers
	 */
	private buildHeaders(customHeaders: Record<string, string> = {}): Record<string, string> {
		const headers = { ...this.config.defaultHeaders, ...customHeaders };

		// Add auth token if available
		if (this.authToken) {
			headers['Authorization'] = `Bearer ${this.authToken}`;
		}

		return headers;
	}

	/**
	 * Create timeout signal
	 */
	private createTimeoutSignal(timeout: number): AbortSignal {
		const controller = new AbortController();
		setTimeout(() => controller.abort(), timeout);
		return controller.signal;
	}

	/**
	 * Handle response and errors
	 */
	private async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
		let data: T;

		// Try to parse JSON response
		try {
			const text = await response.text();
			data = text ? JSON.parse(text) : null;
		} catch (error) {
			// If JSON parsing fails, return the text as data
			data = (await response.text()) as T;
		}

		// Check if response is successful
		if (!response.ok) {
			const errorMessage = this.extractErrorMessage(data);
			throw new ApiClientError(
				errorMessage,
				response.status,
				response.statusText,
				data
			);
		}

		return {
			data,
			status: response.status,
			statusText: response.statusText,
			headers: response.headers,
		};
	}

	/**
	 * Extract error message from response data
	 */
	private extractErrorMessage(data: unknown): string {
		if (typeof data === 'string') return data;
		if (typeof data === 'object' && data !== null) {
			const obj = data as Record<string, unknown>;
			return (obj.message || obj.error || obj.detail || 'An error occurred') as string;
		}
		return 'An error occurred';
	}

	// ========================================================================
	// Core Request Method
	// ========================================================================

	/**
	 * Core request method that all HTTP methods use
	 */
	private async request<T = unknown>(
		method: string,
		endpoint: string,
		body?: unknown,
		options: RequestOptions = {}
	): Promise<ApiResponse<T>> {
		const url = this.buildUrl(endpoint);
		const headers = this.buildHeaders(options.headers);
		const timeout = options.timeout || this.config.timeout;
		const fetchFn = options.fetch || fetch;

		// Create timeout signal if no custom signal provided
		const timeoutSignal = this.createTimeoutSignal(timeout);
		const signal = options.signal || timeoutSignal;

		// Prepare request body
		let requestBody: string | undefined;
		if (body !== undefined) {
			if (typeof body === 'string') {
				requestBody = body;
			} else {
				requestBody = JSON.stringify(body);
			}
		}

		try {
			const response = await fetchFn(url, {
				method,
				headers,
				body: requestBody,
				signal,
			});

			return await this.handleResponse<T>(response);
		} catch (error) {
			// Handle different types of errors
			if (error instanceof ApiClientError) {
				throw error;
			}

			if (error instanceof Error) {
				if (error.name === 'AbortError') {
					throw new TimeoutError(timeout);
				}
				throw new NetworkError(`Network request failed: ${error.message}`, error);
			}

			throw new NetworkError('Unknown network error occurred', error as Error);
		}
	}

	// ========================================================================
	// HTTP Methods
	// ========================================================================

	/**
	 * GET request
	 */
	async get<T = unknown>(endpoint: string, options?: RequestOptions): Promise<ApiResponse<T>> {
		return this.request<T>('GET', endpoint, undefined, options);
	}

	/**
	 * POST request
	 */
	async post<T = unknown>(
		endpoint: string,
		body?: unknown,
		options?: RequestOptions
	): Promise<ApiResponse<T>> {
		return this.request<T>('POST', endpoint, body, options);
	}

	/**
	 * PUT request
	 */
	async put<T = unknown>(
		endpoint: string,
		body?: unknown,
		options?: RequestOptions
	): Promise<ApiResponse<T>> {
		return this.request<T>('PUT', endpoint, body, options);
	}

	/**
	 * PATCH request
	 */
	async patch<T = unknown>(
		endpoint: string,
		body?: unknown,
		options?: RequestOptions
	): Promise<ApiResponse<T>> {
		return this.request<T>('PATCH', endpoint, body, options);
	}

	/**
	 * DELETE request
	 */
	async delete<T = unknown>(endpoint: string, options?: RequestOptions): Promise<ApiResponse<T>> {
		return this.request<T>('DELETE', endpoint, undefined, options);
	}

	// ========================================================================
	// Convenience Methods
	// ========================================================================

	/**
	 * GET request that returns only the data
	 */
	async getData<T = unknown>(endpoint: string, options?: RequestOptions): Promise<T> {
		const response = await this.get<T>(endpoint, options);
		return response.data;
	}

	/**
	 * POST request that returns only the data
	 */
	async postData<T = unknown>(
		endpoint: string,
		body?: unknown,
		options?: RequestOptions
	): Promise<T> {
		const response = await this.post<T>(endpoint, body, options);
		return response.data;
	}

	/**
	 * PUT request that returns only the data
	 */
	async putData<T = unknown>(
		endpoint: string,
		body?: unknown,
		options?: RequestOptions
	): Promise<T> {
		const response = await this.put<T>(endpoint, body, options);
		return response.data;
	}

	/**
	 * PATCH request that returns only the data
	 */
	async patchData<T = unknown>(
		endpoint: string,
		body?: unknown,
		options?: RequestOptions
	): Promise<T> {
		const response = await this.patch<T>(endpoint, body, options);
		return response.data;
	}

	/**
	 * DELETE request that returns only the data
	 */
	async deleteData<T = unknown>(endpoint: string, options?: RequestOptions): Promise<T> {
		const response = await this.delete<T>(endpoint, options);
		return response.data;
	}

	// ========================================================================
	// Utility Methods
	// ========================================================================

	/**
	 * Check if client is authenticated
	 */
	isAuthenticated(): boolean {
		return this.authToken !== null;
	}

	/**
	 * Clear authentication
	 */
	clearAuth(): void {
		this.setAuthToken(null);
	}

	/**
	 * Create a new client instance with custom configuration
	 */
	static create(config?: Partial<ApiConfig>): HttpClient {
		return new HttpClient(config);
	}
}

// ============================================================================
// Default Client Instance
// ============================================================================

/**
 * Default HTTP client instance
 * Use this for most API calls throughout your application
 */
export const apiClient = new HttpClient();

// ============================================================================
// Convenience Functions
// ============================================================================

/**
 * Quick GET request using the default client
 */
export const get = <T = unknown>(endpoint: string, options?: RequestOptions): Promise<ApiResponse<T>> =>
	apiClient.get<T>(endpoint, options);

/**
 * Quick POST request using the default client
 */
export const post = <T = unknown>(
	endpoint: string,
	body?: unknown,
	options?: RequestOptions
): Promise<ApiResponse<T>> => apiClient.post<T>(endpoint, body, options);

/**
 * Quick PUT request using the default client
 */
export const put = <T = unknown>(
	endpoint: string,
	body?: unknown,
	options?: RequestOptions
): Promise<ApiResponse<T>> => apiClient.put<T>(endpoint, body, options);

/**
 * Quick PATCH request using the default client
 */
export const patch = <T = unknown>(
	endpoint: string,
	body?: unknown,
	options?: RequestOptions
): Promise<ApiResponse<T>> => apiClient.patch<T>(endpoint, body, options);

/**
 * Quick DELETE request using the default client
 */
export const del = <T = unknown>(endpoint: string, options?: RequestOptions): Promise<ApiResponse<T>> =>
	apiClient.delete<T>(endpoint, options);

// Data-only convenience functions
export const getData = <T = unknown>(endpoint: string, options?: RequestOptions): Promise<T> =>
	apiClient.getData<T>(endpoint, options);

export const postData = <T = unknown>(
	endpoint: string,
	body?: unknown,
	options?: RequestOptions
): Promise<T> => apiClient.postData<T>(endpoint, body, options);

export const putData = <T = unknown>(
	endpoint: string,
	body?: unknown,
	options?: RequestOptions
): Promise<T> => apiClient.putData<T>(endpoint, body, options);

export const patchData = <T = unknown>(
	endpoint: string,
	body?: unknown,
	options?: RequestOptions
): Promise<T> => apiClient.patchData<T>(endpoint, body, options);

export const deleteData = <T = unknown>(endpoint: string, options?: RequestOptions): Promise<T> =>
	apiClient.deleteData<T>(endpoint, options);

// ============================================================================
// Type Exports (interfaces are already exported above)
// ============================================================================
