/**
 * API Service Layer
 * 
 * This module provides high-level API service functions that use the HTTP client
 * to interact with specific endpoints. Each service function is strongly typed
 * and handles the specific data transformations needed for that endpoint.
 */

import { apiClient } from '../api.js';
import type { RequestOptions } from '../api.js';
import type {
	// Auth types
	LoginRequest,
	LoginResponse,
	RefreshTokenRequest,
	RefreshTokenResponse,
	User,
	
	// Review types
	ReviewItem,
	CreateReviewRequest,
	UpdateReviewRequest,
	ReviewListQuery,
	ReviewListResponse,
	
	// User management types
	UserListQuery,
	UserListResponse,
	CreateUserRequest,
	UpdateUserRequest,
	
	// File upload types
	FileUploadResponse,
	BulkFileUploadResponse,
	
	// Analytics types
	AnalyticsQuery,
	AnalyticsResponse,
	
	// System types
	HealthCheckResponse,
	SystemConfigResponse,
	
	// Common types
	ApiResponseWrapper
} from '../types/api.js';

// ============================================================================
// Authentication Services
// ============================================================================

export const authService = {
	/**
	 * Login user with email and password
	 */
	async login(credentials: LoginRequest, options?: RequestOptions): Promise<LoginResponse> {
		const response = await apiClient.postData<ApiResponseWrapper<LoginResponse>>(
			'/auth/login',
			credentials,
			options
		);
		
		// Set the auth token in the client
		if (response.data.token) {
			apiClient.setAuthToken(response.data.token);
		}
		
		return response.data;
	},

	/**
	 * Logout current user
	 */
	async logout(options?: RequestOptions): Promise<void> {
		try {
			await apiClient.postData('/auth/logout', undefined, options);
		} finally {
			// Always clear the auth token, even if the request fails
			apiClient.clearAuth();
		}
	},

	/**
	 * Refresh authentication token
	 */
	async refreshToken(request: RefreshTokenRequest, options?: RequestOptions): Promise<RefreshTokenResponse> {
		const response = await apiClient.postData<ApiResponseWrapper<RefreshTokenResponse>>(
			'/auth/refresh',
			request,
			options
		);
		
		// Update the auth token in the client
		if (response.data.token) {
			apiClient.setAuthToken(response.data.token);
		}
		
		return response.data;
	},

	/**
	 * Get current user profile
	 */
	async getProfile(options?: RequestOptions): Promise<User> {
		const response = await apiClient.getData<ApiResponseWrapper<User>>('/auth/profile', options);
		return response.data;
	},

	// /**
	//  * Update current user profile
	//  */
	// async updateProfile(updates: Partial<User>, options?: RequestOptions): Promise<User> {
	// 	const response = await apiClient.putData<ApiResponseWrapper<User>>(
	// 		'/auth/profile',
	// 		updates,
	// 		options
	// 	);
	// 	return response.data;
	// },

	// /**
	//  * Change password
	//  */
	// async changePassword(
	// 	data: { currentPassword: string; newPassword: string },
	// 	options?: RequestOptions
	// ): Promise<void> {
	// 	await apiClient.postData('/auth/change-password', data, options);
	// }
};

// ============================================================================
// Review Services
// ============================================================================

export const reviewService = {
	/**
	 * Get list of reviews with optional filtering and pagination
	 */
	async getReviews(query?: ReviewListQuery, options?: RequestOptions): Promise<ReviewListResponse> {
		const searchParams = new URLSearchParams();
		
		if (query) {
			Object.entries(query).forEach(([key, value]) => {
				if (value !== undefined && value !== null) {
					if (Array.isArray(value)) {
						value.forEach(v => searchParams.append(key, String(v)));
					} else {
						searchParams.append(key, String(value));
					}
				}
			});
		}
		
		const endpoint = `/reviews${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
		const response = await apiClient.getData<ApiResponseWrapper<ReviewListResponse>>(endpoint, options);
		return response.data;
	},

	/**
	 * Get a single review by ID
	 */
	async getReview(id: string, options?: RequestOptions): Promise<ReviewItem> {
		const response = await apiClient.getData<ApiResponseWrapper<ReviewItem>>(`/reviews/${id}`, options);
		return response.data;
	},

	/**
	 * Create a new review
	 */
	async createReview(data: CreateReviewRequest, options?: RequestOptions): Promise<ReviewItem> {
		const response = await apiClient.postData<ApiResponseWrapper<ReviewItem>>(
			'/reviews',
			data,
			options
		);
		return response.data;
	},

	/**
	 * Update an existing review
	 */
	async updateReview(id: string, data: UpdateReviewRequest, options?: RequestOptions): Promise<ReviewItem> {
		const response = await apiClient.putData<ApiResponseWrapper<ReviewItem>>(
			`/reviews/${id}`,
			data,
			options
		);
		return response.data;
	},

	/**
	 * Delete a review
	 */
	async deleteReview(id: string, options?: RequestOptions): Promise<void> {
		await apiClient.deleteData(`/reviews/${id}`, options);
	},

	/**
	 * Bulk update reviews
	 */
	async bulkUpdateReviews(
		updates: { id: string; data: UpdateReviewRequest }[],
		options?: RequestOptions
	): Promise<ReviewItem[]> {
		const response = await apiClient.putData<ApiResponseWrapper<ReviewItem[]>>(
			'/reviews/bulk',
			{ updates },
			options
		);
		return response.data;
	}
};

// ============================================================================
// User Management Services
// ============================================================================

export const userService = {
	/**
	 * Get list of users with optional filtering and pagination
	 */
	async getUsers(query?: UserListQuery, options?: RequestOptions): Promise<UserListResponse> {
		const searchParams = new URLSearchParams();
		
		if (query) {
			Object.entries(query).forEach(([key, value]) => {
				if (value !== undefined && value !== null) {
					searchParams.append(key, String(value));
				}
			});
		}
		
		const endpoint = `/users${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
		const response = await apiClient.getData<ApiResponseWrapper<UserListResponse>>(endpoint, options);
		return response.data;
	},

	/**
	 * Get a single user by ID
	 */
	async getUser(id: string, options?: RequestOptions): Promise<User> {
		const response = await apiClient.getData<ApiResponseWrapper<User>>(`/users/${id}`, options);
		return response.data;
	},

	/**
	 * Create a new user
	 */
	async createUser(data: CreateUserRequest, options?: RequestOptions): Promise<User> {
		const response = await apiClient.postData<ApiResponseWrapper<User>>('/users', data, options);
		return response.data;
	},

	/**
	 * Update an existing user
	 */
	async updateUser(id: string, data: UpdateUserRequest, options?: RequestOptions): Promise<User> {
		const response = await apiClient.putData<ApiResponseWrapper<User>>(`/users/${id}`, data, options);
		return response.data;
	},

	/**
	 * Delete a user
	 */
	async deleteUser(id: string, options?: RequestOptions): Promise<void> {
		await apiClient.deleteData(`/users/${id}`, options);
	}
};

// ============================================================================
// File Upload Services
// ============================================================================

export const fileService = {
	/**
	 * Upload a single file
	 */
	async uploadFile(file: File, options?: RequestOptions): Promise<FileUploadResponse> {
		const formData = new FormData();
		formData.append('file', file);
		
		const response = await apiClient.postData<ApiResponseWrapper<FileUploadResponse>>(
			'/files/upload',
			formData,
			{
				...options,
				headers: {
					// Remove Content-Type header to let browser set it with boundary
					...options?.headers,
					'Content-Type': undefined as any
				}
			}
		);
		return response.data;
	},

	/**
	 * Upload multiple files
	 */
	async uploadFiles(files: File[], options?: RequestOptions): Promise<BulkFileUploadResponse> {
		const formData = new FormData();
		files.forEach(file => formData.append('files', file));
		
		const response = await apiClient.postData<ApiResponseWrapper<BulkFileUploadResponse>>(
			'/files/upload/bulk',
			formData,
			{
				...options,
				headers: {
					...options?.headers,
					'Content-Type': undefined as any
				}
			}
		);
		return response.data;
	},

	/**
	 * Delete a file
	 */
	async deleteFile(id: string, options?: RequestOptions): Promise<void> {
		await apiClient.deleteData(`/files/${id}`, options);
	}
};

// ============================================================================
// Analytics Services
// ============================================================================

export const analyticsService = {
	/**
	 * Get analytics data
	 */
	async getAnalytics(query: AnalyticsQuery, options?: RequestOptions): Promise<AnalyticsResponse> {
		const searchParams = new URLSearchParams();

		Object.entries(query).forEach(([key, value]) => {
			if (value !== undefined && value !== null) {
				if (Array.isArray(value)) {
					value.forEach(v => searchParams.append(key, String(v)));
				} else if (typeof value === 'object') {
					searchParams.append(key, JSON.stringify(value));
				} else {
					searchParams.append(key, String(value));
				}
			}
		});

		const endpoint = `/analytics?${searchParams.toString()}`;
		const response = await apiClient.getData<ApiResponseWrapper<AnalyticsResponse>>(endpoint, options);
		return response.data;
	},

	/**
	 * Get dashboard summary data
	 */
	async getDashboardSummary(options?: RequestOptions): Promise<Record<string, unknown>> {
		const response = await apiClient.getData<ApiResponseWrapper<Record<string, unknown>>>(
			'/analytics/dashboard',
			options
		);
		return response.data;
	}
};

// ============================================================================
// System Services
// ============================================================================

export const systemService = {
	/**
	 * Get system health status
	 */
	async getHealth(options?: RequestOptions): Promise<HealthCheckResponse> {
		const response = await apiClient.getData<HealthCheckResponse>('/health', options);
		return response;
	},

	/**
	 * Get system configuration
	 */
	async getConfig(options?: RequestOptions): Promise<SystemConfigResponse> {
		const response = await apiClient.getData<ApiResponseWrapper<SystemConfigResponse>>(
			'/system/config',
			options
		);
		return response.data;
	}
};

// ============================================================================
// Utility Functions
// ============================================================================

/**
 * Helper function to handle API errors consistently
 */
export function handleApiError(error: unknown): string {
	if (error && typeof error === 'object' && 'message' in error) {
		return (error as { message: string }).message;
	}

	if (typeof error === 'string') {
		return error;
	}

	return 'An unexpected error occurred';
}

/**
 * Helper function to build query parameters from an object
 */
export function buildQueryParams(params: Record<string, unknown>): URLSearchParams {
	const searchParams = new URLSearchParams();

	Object.entries(params).forEach(([key, value]) => {
		if (value !== undefined && value !== null) {
			if (Array.isArray(value)) {
				value.forEach(v => searchParams.append(key, String(v)));
			} else if (typeof value === 'object') {
				searchParams.append(key, JSON.stringify(value));
			} else {
				searchParams.append(key, String(value));
			}
		}
	});

	return searchParams;
}

/**
 * Helper function to create a custom API client with different configuration
 */
export async function createCustomApiClient(baseUrl: string, defaultHeaders?: Record<string, string>) {
	const { HttpClient } = await import('../api.js');
	return new HttpClient({
		baseUrl,
		defaultHeaders: {
			'Content-Type': 'application/json',
			'Accept': 'application/json',
			...defaultHeaders
		}
	});
}
