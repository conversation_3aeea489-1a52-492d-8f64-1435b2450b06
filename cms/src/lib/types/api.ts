/**
 * API Types for HTTP Client
 * 
 * This file contains TypeScript types for API requests and responses
 * to provide strong typing throughout the application.
 */

import type { UserRole } from './auth.js';

// ============================================================================
// Common API Types
// ============================================================================

/**
 * Standard API response wrapper
 */
export interface ApiResponseWrapper<T = unknown> {
	success: boolean;
	data: T;
	message?: string;
	errors?: string[];
	meta?: {
		pagination?: PaginationMeta;
		timestamp?: string;
		version?: string;
	};
}

/**
 * Pagination metadata
 */
export interface PaginationMeta {
	page: number;
	limit: number;
	total: number;
	totalPages: number;
	hasNext: boolean;
	hasPrev: boolean;
}

/**
 * Standard error response
 */
export interface ApiErrorResponse {
	success: false;
	message: string;
	errors?: string[];
	code?: string;
	details?: Record<string, unknown>;
}

// ============================================================================
// Authentication API Types
// ============================================================================

/**
 * Login request payload
 */
export interface LoginRequest {
	email: string;
	password: string;
}

/**
 * Login response data
 */
export interface LoginResponse {
	user: User;
	token: string;
	refreshToken?: string;
	expiresIn?: number;
}

/**
 * Token refresh request
 */
export interface RefreshTokenRequest {
	refreshToken: string;
}

/**
 * Token refresh response
 */
export interface RefreshTokenResponse {
	token: string;
	refreshToken?: string;
	expiresIn?: number;
}

/**
 * User profile data
 */
export interface User {
	id: string;
	name: string;
	email: string;
	avatar?: string;
	role: UserRole;
	createdAt?: string;
	updatedAt?: string;
	lastLoginAt?: string;
}

// ============================================================================
// Review System API Types
// ============================================================================

/**
 * Review item data
 */
export interface ReviewItem {
	id: string;
	title: string;
	description?: string;
	status: ReviewStatus;
	priority: ReviewPriority;
	assignedTo?: User;
	createdBy: User;
	createdAt: string;
	updatedAt: string;
	dueDate?: string;
	tags?: string[];
	metadata?: Record<string, unknown>;
}

/**
 * Review status enum
 */
export enum ReviewStatus {
	PENDING = 'pending',
	IN_PROGRESS = 'in_progress',
	COMPLETED = 'completed',
	REJECTED = 'rejected',
	ARCHIVED = 'archived'
}

/**
 * Review priority enum
 */
export enum ReviewPriority {
	LOW = 'low',
	MEDIUM = 'medium',
	HIGH = 'high',
	URGENT = 'urgent'
}

/**
 * Create review request
 */
export interface CreateReviewRequest {
	title: string;
	description?: string;
	priority?: ReviewPriority;
	assignedToId?: string;
	dueDate?: string;
	tags?: string[];
	metadata?: Record<string, unknown>;
}

/**
 * Update review request
 */
export interface UpdateReviewRequest {
	title?: string;
	description?: string;
	status?: ReviewStatus;
	priority?: ReviewPriority;
	assignedToId?: string;
	dueDate?: string;
	tags?: string[];
	metadata?: Record<string, unknown>;
}

/**
 * Review list query parameters
 */
export interface ReviewListQuery {
	page?: number;
	limit?: number;
	status?: ReviewStatus;
	priority?: ReviewPriority;
	assignedToId?: string;
	createdById?: string;
	search?: string;
	tags?: string[];
	sortBy?: 'createdAt' | 'updatedAt' | 'dueDate' | 'priority' | 'title';
	sortOrder?: 'asc' | 'desc';
	dateFrom?: string;
	dateTo?: string;
}

/**
 * Review list response
 */
export interface ReviewListResponse {
	items: ReviewItem[];
	pagination: PaginationMeta;
	filters?: {
		statuses: ReviewStatus[];
		priorities: ReviewPriority[];
		assignees: User[];
		tags: string[];
	};
}

// ============================================================================
// User Management API Types
// ============================================================================

/**
 * User list query parameters
 */
export interface UserListQuery {
	page?: number;
	limit?: number;
	role?: UserRole;
	search?: string;
	sortBy?: 'name' | 'email' | 'role' | 'createdAt' | 'lastLoginAt';
	sortOrder?: 'asc' | 'desc';
	active?: boolean;
}

/**
 * User list response
 */
export interface UserListResponse {
	items: User[];
	pagination: PaginationMeta;
}

/**
 * Create user request
 */
export interface CreateUserRequest {
	name: string;
	email: string;
	role: UserRole;
	password?: string;
	avatar?: string;
}

/**
 * Update user request
 */
export interface UpdateUserRequest {
	name?: string;
	email?: string;
	role?: UserRole;
	avatar?: string;
	active?: boolean;
}

// ============================================================================
// File Upload API Types
// ============================================================================

/**
 * File upload response
 */
export interface FileUploadResponse {
	id: string;
	filename: string;
	originalName: string;
	mimeType: string;
	size: number;
	url: string;
	thumbnailUrl?: string;
	uploadedAt: string;
}

/**
 * Bulk file upload response
 */
export interface BulkFileUploadResponse {
	successful: FileUploadResponse[];
	failed: {
		filename: string;
		error: string;
	}[];
}

// ============================================================================
// Analytics API Types
// ============================================================================

/**
 * Analytics query parameters
 */
export interface AnalyticsQuery {
	dateFrom: string;
	dateTo: string;
	granularity?: 'hour' | 'day' | 'week' | 'month';
	metrics?: string[];
	filters?: Record<string, unknown>;
}

/**
 * Analytics data point
 */
export interface AnalyticsDataPoint {
	timestamp: string;
	value: number;
	label?: string;
	metadata?: Record<string, unknown>;
}

/**
 * Analytics response
 */
export interface AnalyticsResponse {
	metrics: {
		[metricName: string]: {
			data: AnalyticsDataPoint[];
			total?: number;
			average?: number;
			change?: number;
			changePercent?: number;
		};
	};
	period: {
		from: string;
		to: string;
		granularity: string;
	};
}

// ============================================================================
// System API Types
// ============================================================================

/**
 * System health check response
 */
export interface HealthCheckResponse {
	status: 'healthy' | 'degraded' | 'unhealthy';
	timestamp: string;
	version: string;
	services: {
		[serviceName: string]: {
			status: 'up' | 'down' | 'degraded';
			responseTime?: number;
			lastCheck: string;
			details?: Record<string, unknown>;
		};
	};
}

/**
 * System configuration response
 */
export interface SystemConfigResponse {
	features: {
		[featureName: string]: boolean;
	};
	limits: {
		maxFileSize: number;
		maxFilesPerUpload: number;
		sessionTimeout: number;
		rateLimit: {
			requests: number;
			window: number;
		};
	};
	version: string;
	environment: string;
}
