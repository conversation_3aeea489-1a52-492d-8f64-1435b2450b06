<script lang="ts">
	import '../app.css';

	import { page } from '$app/state';
	import { setAuthStore } from '$lib/stores/auth.svelte.js';
	import { setNavigationStore } from '$lib/stores/navigation.svelte.js';
	import { syncNavigationWithPage } from '$lib/stores/navigation-utils.js';
	import ListDetailsIcon from '@tabler/icons-svelte/icons/list-details';

	let { children } = $props();

	// Initialize auth store first
	setAuthStore();

	// Initialize navigation store
	const navigationStore = setNavigationStore({
		headerTitle: 'Dashboard',
		currentRoute: '/',
		items: [
			{
				id: 'reviews',
				title: 'Reviews',
				url: '/reviews',
				icon: ListDetailsIcon,
				badge: 3
			}
		]
	});

	// Auto-sync navigation store with page changes
	$effect(() => {
		syncNavigationWithPage(navigationStore, page);
	});
</script>

{@render children()}
