// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider     = "mysql"
  url          = env("DATABASE_URL")
  relationMode = "prisma"
}

model InteractionEvent {
  id              String  @id @default(cuid())
  appId           String
  loggedFromAppId String?
  sessionId       String
  eventType       String
  pageUrl         String
  ipAddress       String?
  userAgent       String? @db.Text

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("interaction_events")
}

model LLMGeneration {
  id            String  @id @default(cuid())
  appId         String
  sessionId     String
  model         String
  excludedWords String? @db.Text
  removedWords  String? @db.Text
  prompt        String? @db.Text
  response      String? @db.LongText
  topics        Json?
  insights      Json?
  usageMetadata String? @db.Text
  duration      Int     @default(0)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("llm_generations")
}

enum UserRole {
  ADMIN
  USER
}

model User {
  id       String   @id @default(cuid())
  email    String   @unique
  password String
  role     UserRole @default(USER)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("users")
}

enum AssetGenerationType {
  IMAGE_MIDJOURNEY
  IMAGE_GEN_4
  IMAGE_FLUX_11_PRO
  IMAGE_FLUX_11_PRO_ULTRA
  IMAGE_FLUX_KONTEXT_PRO
  VIDEO_KLING
  VIDEO_LUMA
  INPAINTING_FLUX
  INPAINTING_SD
}

model AssetGeneration {
  id             String              @id @default(cuid())
  appId          String
  sessionId      String
  taskId         String?             @unique @default(cuid())
  prompt         String?             @db.Text //This is for image generation
  prompts        Json?               @default("[]") //This is for co-create multiple prompts
  expectedAmount Int?
  inputImageUrl  String?             @db.Text //This is for video generation only
  inpaintInput   Json?               @default("{}") // This is for inpainting generation, stores the input image and mask
  type           AssetGenerationType @default(IMAGE_MIDJOURNEY)
  assetUrls      GeneratedAsset[]
  duration       Int                 @default(0)
  status         String              @default("pending")

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("asset_generations")
}

model GeneratedAsset {
  id                String           @id @default(cuid())
  appId             String
  sessionId         String
  model             String           @default("unknown")
  rawFilename       String?
  filename          String
  vote              Int              @default(0)
  likeCount         Int              @default(0)
  assetGeneration   AssetGeneration? @relation(fields: [assetGenerationId], references: [id])
  assetGenerationId String?

  fromImageId String? @unique

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([assetGenerationId])
  @@map("generated_assets")
}

model TempGeneratedAsset {
  id        String @id @default(cuid())
  appId     String
  sessionId String
  filename  String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("temp_generated_assets")
}

model GeneratedComment {
  id        String @id @default(cuid())
  appId     String
  sessionId String
  model     String @default("unknown")

  personaKey String?
  persona    String
  comment    String  @db.Text

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([sessionId, personaKey])
  @@map("generated_comments")
}

enum TrackType {
  DIRECT_GENERATION
  INPAINTING_GENERATION
}

model Sites {
  id                 String          @id @default(cuid())
  key                String          @unique
  name               String
  mapName            String
  preamble           String          @db.Text
  keyCharacteristics String          @db.Text
  tagLine            String          @db.Text
  excludedWords      String?         @db.Text
  trackType          TrackType
  questions          SiteQuestions[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("sites")
}

model SiteQuestions {
  id       String        @id @default(cuid())
  sites    Sites?        @relation(fields: [siteId], references: [id])
  siteId   String?
  order    Int
  mode     GameStateType
  question String        @db.Text
  hint     String?       @db.Text
  options  Json          @default("[]")

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([siteId])
  @@map("site_questions")
}

model Personas {
  id          String @id @default(cuid())
  key         String
  name        String
  description String @db.Text
  dmp         String @db.Text

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("personas")
}

enum GameStateType {
  INDIVIDUAL
  GROUP
}

model GameState {
  id        String @id @default(cuid())
  appId     String
  sessionId String @unique

  type GameStateType
  name String?

  siteId       String?
  siteSnapshot Json?

  personaId       String?
  personaSnapshot Json?

  generatedImageId String?
  generatedVideoId String?

  active   Boolean @default(true)
  reviewed Boolean @default(false)

  gameQA   GameQA[]
  feedback Feedback[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("game_states")
}

enum EmailType {
  GENERATED
  PHOTOBOOTH
}

model Email {
  id        String    @id @default(cuid())
  sessionId String
  type      EmailType @default(GENERATED)
  email     String
  emailSent Boolean   @default(false)
  imageId   String?
  videoId   String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("emails")
}

model Feedback {
  id        String @id @default(cuid())
  appId     String
  sessionId String

  rate     Int
  feedback String? @db.Text

  gameState   GameState? @relation(fields: [gameStateId], references: [id])
  gameStateId String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([gameStateId])
  @@map("feebback")
}

model GameQA {
  id        String @id @default(cuid())
  appId     String
  sessionId String

  order    Int
  question String
  answer   String? @db.LongText
  closed   Boolean @default(false)

  gameState   GameState @relation(fields: [gameStateId], references: [id])
  gameStateId String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([gameStateId])
  @@map("game_qa")
}

model WordsCloud {
  id String @id @default(cuid())

  word    String
  siteKey String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([word, siteKey])
  @@map("words_cloud")
}

model Setting {
  id String @id @default(cuid())

  key   String
  value String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([key])
  @@map("settings")
}
